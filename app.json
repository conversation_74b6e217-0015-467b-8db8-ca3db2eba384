{"pages": ["pages/index/index", "pages/basics/home/<USER>", "pages/toolbox/home/<USER>", "pages/funpic/home/<USER>", "pages/profile/home/<USER>", "pages/profile/log/log", "pages/basics/filePreview/filePreview", "pages/webview/webview", "pages/toolbox/wordGuessing/wordGuessing", "pages/toolbox/uglyAvatar/uglyAvatar", "pages/toolbox/cutout/cutout", "pages/random/home/<USER>", "pages/toolbox/idPhoto/idPhoto", "pages/toolbox/acronymTranslator/acronymTranslator"], "subPackages": [{"root": "pages/subpage", "pages": ["pages/certificates/certificates", "pages/certificatesEdit/certificatesEdit", "pages/idPhoto/idPhoto", "pages/idPhotoPreview/idPhotoPreview", "pages/picEdit/picEdit", "pages/picGen/picGen", "pages/picSize/picSize", "pages/takePhoto/takePhoto"]}], "window": {"navigationBarBackgroundColor": "#39b54a", "navigationBarTitleText": "allbs", "navigationStyle": "custom", "navigationBarTextStyle": "white"}, "style": "v2", "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "usingComponents": {"cu-custom": "/colorui/components/cu-custom", "funpic": "/pages/funpic/home/<USER>"}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "downloadFile": 60000, "uploadFile": 60000}}