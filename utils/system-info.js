// 系统信息兼容性工具函数
// 用于替代废弃的 wx.getSystemInfoSync()

/**
 * 获取窗口信息（替代 wx.getSystemInfoSync().windowWidth/windowHeight）
 */
export function getWindowInfo() {
    try {
        return wx.getWindowInfo();
    } catch (error) {
        console.warn('wx.getWindowInfo() 不可用，使用备用方案');
        try {
            // 备用方案：使用废弃的API
            const info = wx.getSystemInfoSync();
            return {
                windowWidth: info.windowWidth,
                windowHeight: info.windowHeight,
                pixelRatio: info.pixelRatio
            };
        } catch (fallbackError) {
            console.error('获取窗口信息失败:', fallbackError);
            // 返回默认值
            return {
                windowWidth: 375,
                windowHeight: 667,
                pixelRatio: 2
            };
        }
    }
}

/**
 * 获取设备信息（替代 wx.getSystemInfoSync().pixelRatio等）
 */
export function getDeviceInfo() {
    try {
        return wx.getDeviceInfo();
    } catch (error) {
        console.warn('wx.getDeviceInfo() 不可用，使用备用方案');
        try {
            const info = wx.getSystemInfoSync();
            return {
                pixelRatio: info.pixelRatio,
                system: info.system,
                platform: info.platform
            };
        } catch (fallbackError) {
            console.error('获取设备信息失败:', fallbackError);
            return {
                pixelRatio: 2,
                system: 'unknown',
                platform: 'unknown'
            };
        }
    }
}

/**
 * 获取应用基础信息
 */
export function getAppBaseInfo() {
    try {
        return wx.getAppBaseInfo();
    } catch (error) {
        console.warn('wx.getAppBaseInfo() 不可用，使用备用方案');
        try {
            const info = wx.getSystemInfoSync();
            return {
                version: info.version,
                SDKVersion: info.SDKVersion
            };
        } catch (fallbackError) {
            console.error('获取应用信息失败:', fallbackError);
            return {
                version: 'unknown',
                SDKVersion: 'unknown'
            };
        }
    }
}

/**
 * 获取完整的系统信息（兼容旧代码）
 */
export function getSystemInfo() {
    try {
        const windowInfo = getWindowInfo();
        const deviceInfo = getDeviceInfo();
        const appInfo = getAppBaseInfo();
        
        return {
            ...windowInfo,
            ...deviceInfo,
            ...appInfo
        };
    } catch (error) {
        console.error('获取系统信息失败:', error);
        // 最后的备用方案
        try {
            return wx.getSystemInfoSync();
        } catch (finalError) {
            console.error('所有获取系统信息的方法都失败了:', finalError);
            return {
                windowWidth: 375,
                windowHeight: 667,
                pixelRatio: 2,
                system: 'unknown',
                platform: 'unknown',
                version: 'unknown',
                SDKVersion: 'unknown'
            };
        }
    }
}
