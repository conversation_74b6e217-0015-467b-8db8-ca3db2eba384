# 工具综合小程序项目
## 小程序体验
![小程序体验链接](https://pic.tuytuy.com/share/xcx_share.jpg)

## 项目介绍
这是一个微信小程序项目，使用微信原生开发，集成了多种实用工具，包括一键抠图、证件照制作、图片裁剪、热点新闻阅读、在线文件预览等功能。

## 功能特点
- 证件照制作与编辑（拍照、换底色、调整大小等）
- 图片处理工具（裁剪、美化、去底等）
- 热点新闻阅读（小程序需要企业认证）
- 在线文件预览（需企业认证）
- 工具箱（缩略语翻译器、丑丑头像生成器、一键抠图）

## 后端
因为后端代码涉及不少个人服务不便开源，所以需要自行搭建对应的后端服务。我会将所有api接口的返回格式和参数说明放在下面，方便大家进行后端服务的搭建。

## 技术栈
- 微信小程序原生开发
- minio的文件储存，避免小程序内容过大，无法上架，java中的minio操作工具使用的是[chenqi92/alltobs-oss](https://github.com/chenqi92/alltobs-oss)
- ColorUI 组件库 [weilanwl/coloruicss](https://github.com/weilanwl/coloruicss)
- kkFileView 在线预览组件[kekingcn/kkFileView](https://github.com/kekingcn/kkFileView)
- 新闻热搜，使用的是库[imsyy/DailyHotApi](https://github.com/imsyy/DailyHotApi)然后缓存至redis中，之后使用timescaledb进行储存，相关储存逻辑代码为[chenqi92/dailyhot-data-save](https://github.com/chenqi92/dailyhot-data-save)
- 抠图，使用的是开源模型RMBG-1.4，写了一个python web服务进行的转换

## 安装与使用
1. 克隆项目
2. 使用微信开发者工具打开项目
3. 修改后端API地址配置（详见配置说明）
4. 编译运行

## 项目结构
```
├── components/    # 组件
├── pages/         # 页面
│   ├── basics/    # 证件照相关
│   ├── toolbox/   # 工具箱
│   ├── random/    # 热点新闻
│   ├── profile/   # 个人中心
│   ├── funpic/    # 趣图大全
│   ├── subpage/   # 子页面
│   └── webview/   # 网页视图
├── images/        # 图片资源
├── utils/         # 工具函数
│   ├── api.js     # API接口配置
│   └── request.js # 请求封装
├── colorui/       # UI组件库
└── app.js         # 入口文件
```

## 后端API配置说明
后端API地址需要在 `/utils/api.js` 文件中配置，目前默认配置为：
```javascript
const API_BASE_URL = 你的后端api地址; // 例如：const API_BASE_URL = "http://localhost:8888/";
const SERVER_URL = 你的图片访问前缀; // 例如：const SERVER_URL = "http://localhost:9000/";
const PREVIEW_BASE_URL = 你的文件预览地址; // 例如：const PREVIEW_BASE_URL = "http://localhost:9000/";
```

⚠️ **重要提示**：
- 后端API服务暂时开放，后续会下架，需自行搭建对应的后端服务
- 在线预览和新闻列表功能需要企业认证微信小程序才能使用

## API接口列表

### 热点新闻相关
- `wx/hot/sideBarList` - 获取新闻侧边栏列表
```json
{
  "code": 200,
  "msg": "SUCCESS",
  "data": [
    {
      "path": "/tieba",
      "name": "tieba",
      "chineseName": "贴吧",
      "sort": 2
    },
    {
      "path": "/toutiao",
      "name": "toutiao",
      "chineseName": "今日头条",
      "sort": 3
    },
    {
      "path": "/thepaper",
      "name": "thepaper",
      "chineseName": "澎湃新闻",
      "sort": 3
    },
    {
      "path": "/sina",
      "name": "sina",
      "chineseName": "新浪",
      "sort": 5
    },
    {
      "path": "/sina-news",
      "name": "sina-news",
      "chineseName": "新浪新闻",
      "sort": 6
    },
    {
      "path": "/qq-news",
      "name": "qq-news",
      "chineseName": "腾讯新闻",
      "sort": 7
    },
    {
      "path": "/netease-news",
      "name": "netease-news",
      "chineseName": "网易新闻",
      "sort": 8
    },
    {
      "path": "/hupu",
      "name": "hupu",
      "chineseName": "虎扑",
      "sort": 9
    },
    {
      "path": "/douyin",
      "name": "douyin",
      "chineseName": "抖音",
      "sort": 10
    },
    {
      "path": "/baidu",
      "name": "baidu",
      "chineseName": "百度",
      "sort": 11
    },
    {
      "path": "/bilibili",
      "name": "bilibili",
      "chineseName": "哔哩哔哩",
      "sort": 12
    },
    {
      "path": "/douban-group",
      "name": "douban-group",
      "chineseName": "豆瓣小组",
      "sort": 13
    },
    {
      "path": "/douban-movie",
      "name": "douban-movie",
      "chineseName": "豆瓣电影",
      "sort": 14
    },
    {
      "path": "/jianshu",
      "name": "jianshu",
      "chineseName": "简书",
      "sort": 15
    },
    {
      "path": "/ngabbs",
      "name": "ngabbs",
      "chineseName": "NGA",
      "sort": 16
    },
    {
      "path": "/sspai",
      "name": "sspai",
      "chineseName": "少数派",
      "sort": 17
    },
    {
      "path": "/juejin",
      "name": "juejin",
      "chineseName": "掘金",
      "sort": 22
    },
    {
      "path": "/history",
      "name": "history",
      "chineseName": "历史",
      "sort": 23
    },
    {
      "path": "/hellogithub",
      "name": "hellogithub",
      "chineseName": "HelloGitHub",
      "sort": 24
    },
    {
      "path": "/36kr",
      "name": "36kr",
      "chineseName": "36氪",
      "sort": 25
    },
    {
      "path": "/51cto",
      "name": "51cto",
      "chineseName": "51CTO",
      "sort": 26
    },
    {
      "path": "/acfun",
      "name": "acfun",
      "chineseName": "AcFun",
      "sort": 27
    },
    {
      "path": "/csdn",
      "name": "csdn",
      "chineseName": "CSDN",
      "sort": 29
    },
    {
      "path": "/earthquake",
      "name": "earthquake",
      "chineseName": "地震",
      "sort": 30
    },
    {
      "path": "/huxiu",
      "name": "huxiu",
      "chineseName": "虎嗅",
      "sort": 32
    },
    {
      "path": "/ifanr",
      "name": "ifanr",
      "chineseName": "爱范儿",
      "sort": 33
    },
    {
      "path": "/ithome",
      "name": "ithome",
      "chineseName": "IT之家",
      "sort": 34
    },
    {
      "path": "/ithome-xijiayi",
      "name": "ithome-xijiayi",
      "chineseName": "喜加1",
      "sort": 35
    }
  ],
  "ok": true
}
```

- `wx/hot/getHotNews` - 获取热点新闻 以贴吧为例 请求路径 `api/wx/hot/getHotNews/tieba`
```json
{
  "code": 200,
  "msg": "SUCCESS",
  "data": [
    {
      "title": "甲亢哥大张伟Boss战就在今晚",
      "desc": "主线任务来了！据湖南卫视，甲亢哥与大张伟今晚将在芒果新综艺录制现场见面，吧友们对两人会面有哪些期待？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/cc11728b4710b9124d873ed385fdfc039245225d.jpg?tbpicau=2025-04-19-05_09f5a780f8e24c031d4272997cdf55e1",
      "hot": 262674,
      "sort": null,
      "timestamp": "1743908243000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341092&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E5%A4%A7%E5%BC%A0%E4%BC%9FBoss%E6%88%98%E5%B0%B1%E5%9C%A8%E4%BB%8A%E6%99%9A",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341092&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E5%A4%A7%E5%BC%A0%E4%BC%9FBoss%E6%88%98%E5%B0%B1%E5%9C%A8%E4%BB%8A%E6%99%9A"
    },
    {
      "title": "吧友爆料苏丹的游戏幕后推手",
      "desc": "吧友爆料《苏丹的游戏》幕后推手：制作方并非9人独立游戏团队，与媒体合谋欺骗玩家，试图规训玩家接受游戏中的DEI内容。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/a9d3fd1f4134970a5fc62501d3cad1c8a7865d62.jpg?tbpicau=2025-04-19-05_e4a23fc2ef2374af0ce25b091d979c43",
      "hot": 18812,
      "sort": null,
      "timestamp": "1743922747000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341100&amp;topic_name=%E5%90%A7%E5%8F%8B%E7%88%86%E6%96%99%E8%8B%8F%E4%B8%B9%E7%9A%84%E6%B8%B8%E6%88%8F%E5%B9%95%E5%90%8E%E6%8E%A8%E6%89%8B",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341100&amp;topic_name=%E5%90%A7%E5%8F%8B%E7%88%86%E6%96%99%E8%8B%8F%E4%B8%B9%E7%9A%84%E6%B8%B8%E6%88%8F%E5%B9%95%E5%90%8E%E6%8E%A8%E6%89%8B"
    },
    {
      "title": "向鹏强势横扫李尚洙夺冠",
      "desc": "未来可期！WTT仁川冠军赛男单决赛，国乒新生代选手向鹏 4-0 横扫老将李尚洙，夺得自己的首个WTT冠军赛冠军。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/f11f3a292df5e0fec855bd2f1a6034a85edf724a.jpg?tbpicau=2025-04-19-05_cc44b6cf22c5eba9ec1ec0eb407022ab",
      "hot": 15398,
      "sort": null,
      "timestamp": "1743926797000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341102&amp;topic_name=%E5%90%91%E9%B9%8F%E5%BC%BA%E5%8A%BF%E6%A8%AA%E6%89%AB%E6%9D%8E%E5%B0%9A%E6%B4%99%E5%A4%BA%E5%86%A0",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341102&amp;topic_name=%E5%90%91%E9%B9%8F%E5%BC%BA%E5%8A%BF%E6%A8%AA%E6%89%AB%E6%9D%8E%E5%B0%9A%E6%B4%99%E5%A4%BA%E5%86%A0"
    },
    {
      "title": "美国50万人游行抗议特朗普",
      "desc": "美国爆发大规模反特朗普示威，超50万人参加1200场抗议活动，反对政府削减开支、打击移民和加征关税等。你认为特朗普关税战能坚持多久？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/4afbfbedab64034fd667f628e9c379310a551d71.jpg?tbpicau=2025-04-19-05_ea59eebc0418a0a05feb75dc212fbaeb",
      "hot": 19300,
      "sort": null,
      "timestamp": "1743928521000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341103&amp;topic_name=%E7%BE%8E%E5%9B%BD50%E4%B8%87%E4%BA%BA%E6%B8%B8%E8%A1%8C%E6%8A%97%E8%AE%AE%E7%89%B9%E6%9C%97%E6%99%AE",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341103&amp;topic_name=%E7%BE%8E%E5%9B%BD50%E4%B8%87%E4%BA%BA%E6%B8%B8%E8%A1%8C%E6%8A%97%E8%AE%AE%E7%89%B9%E6%9C%97%E6%99%AE"
    },
    {
      "title": "新三国离谱程度同人小说都拍不出来",
      "desc": "新三国吐槽真的是常看常新，每个吐槽都有各自的角度，网友表示看起点小说三国同人文都不会拍出这么离谱的剧。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/9345d688d43f879420927b00941b0ef41bd53aaa.jpg?tbpicau=2025-04-19-05_c70d1d57dda8768155354ada514c6ec6",
      "hot": 16581,
      "sort": null,
      "timestamp": "1743947445000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341109&amp;topic_name=%E6%96%B0%E4%B8%89%E5%9B%BD%E7%A6%BB%E8%B0%B1%E7%A8%8B%E5%BA%A6%E5%90%8C%E4%BA%BA%E5%B0%8F%E8%AF%B4%E9%83%BD%E6%8B%8D%E4%B8%8D%E5%87%BA%E6%9D%A5",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341109&amp;topic_name=%E6%96%B0%E4%B8%89%E5%9B%BD%E7%A6%BB%E8%B0%B1%E7%A8%8B%E5%BA%A6%E5%90%8C%E4%BA%BA%E5%B0%8F%E8%AF%B4%E9%83%BD%E6%8B%8D%E4%B8%8D%E5%87%BA%E6%9D%A5"
    },
    {
      "title": "甲亢哥的逆天翻译被央视打码",
      "desc": "甲亢哥中国行引发关注，而翻译却成了此次行程的败笔，其中混血网红李美越在担任翻译时恶意曲解他人的意思，令人大感不适。网友在央视的报道中发现此人已被打码，这是彻底给他定性了吧？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/738b4710b912c8fcbe42c5e3ba039245d688216e.jpg?tbpicau=2025-04-19-05_1b03471ee0f506f48f826698e460d6ef",
      "hot": 26496,
      "sort": null,
      "timestamp": "1743948610000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341111&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E7%9A%84%E9%80%86%E5%A4%A9%E7%BF%BB%E8%AF%91%E8%A2%AB%E5%A4%AE%E8%A7%86%E6%89%93%E7%A0%81",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341111&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E7%9A%84%E9%80%86%E5%A4%A9%E7%BF%BB%E8%AF%91%E8%A2%AB%E5%A4%AE%E8%A7%86%E6%89%93%E7%A0%81"
    },
    {
      "title": "清明档票房10年来最差",
      "desc": "截至4月6日下午16点，清明档报收3.5亿元。这个成绩对比历年来的清明档，如果刨除2022年的疫情影响，2023年仅有1天的档期因素，今年清明档已然创下10年来最低的票房收入。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/e850352ac65c10384702fcecf4119313b07e899b.jpg?tbpicau=2025-04-19-05_80f086e270aa1267c0bf57047d765377",
      "hot": 14472,
      "sort": null,
      "timestamp": "1743951210000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341112&amp;topic_name=%E6%B8%85%E6%98%8E%E6%A1%A3%E7%A5%A8%E6%88%BF10%E5%B9%B4%E6%9D%A5%E6%9C%80%E5%B7%AE",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341112&amp;topic_name=%E6%B8%85%E6%98%8E%E6%A1%A3%E7%A5%A8%E6%88%BF10%E5%B9%B4%E6%9D%A5%E6%9C%80%E5%B7%AE"
    },
    {
      "title": "iG脆败BLG差距在哪",
      "desc": "LPL第二赛段组内赛焦点战，BLG 2-0 顺利击败iG，这个iG谁的问题最大？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/bf096b63f6246b60f7ef805aadf81a4c510fa23f.jpg?tbpicau=2025-04-19-05_4679dad0ca1cd63fca542512b2045fdc",
      "hot": 21114,
      "sort": null,
      "timestamp": "1743954208000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341113&amp;topic_name=iG%E8%84%86%E8%B4%A5BLG%E5%B7%AE%E8%B7%9D%E5%9C%A8%E5%93%AA",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341113&amp;topic_name=iG%E8%84%86%E8%B4%A5BLG%E5%B7%AE%E8%B7%9D%E5%9C%A8%E5%93%AA"
    },
    {
      "title": "败犬女主太多了第2季制作决定",
      "desc": "《败犬女主太多了》TV动画第2季官宣制作，又可以见到可爱的老八了。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/f3d3572c11dfa9ec51206b3924d0f703918fc196.jpg?tbpicau=2025-04-19-05_5212796dccde85e4caef2c95425eaa38",
      "hot": 33129,
      "sort": null,
      "timestamp": "1743955285000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341114&amp;topic_name=%E8%B4%A5%E7%8A%AC%E5%A5%B3%E4%B8%BB%E5%A4%AA%E5%A4%9A%E4%BA%86%E7%AC%AC2%E5%AD%A3%E5%88%B6%E4%BD%9C%E5%86%B3%E5%AE%9A",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341114&amp;topic_name=%E8%B4%A5%E7%8A%AC%E5%A5%B3%E4%B8%BB%E5%A4%AA%E5%A4%9A%E4%BA%86%E7%AC%AC2%E5%AD%A3%E5%88%B6%E4%BD%9C%E5%86%B3%E5%AE%9A"
    },
    {
      "title": "宋清辉要和胖东来死磕到底",
      "desc": "宋清辉称自己是在揭露真相，胖东来就是靠制造流量乱象存活，干涉员工私生活就是无赖行径。言论自由不应受地方企业钳制，自己要斗争到底。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/3c6d55fbb2fb43168bf3445466a4462309f7d334.jpg?tbpicau=2025-04-19-05_293eb5f19a32b8433233378d5e119c5f",
      "hot": 45036,
      "sort": null,
      "timestamp": "1743985771000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341116&amp;topic_name=%E5%AE%8B%E6%B8%85%E8%BE%89%E8%A6%81%E5%92%8C%E8%83%96%E4%B8%9C%E6%9D%A5%E6%AD%BB%E7%A3%95%E5%88%B0%E5%BA%95",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341116&amp;topic_name=%E5%AE%8B%E6%B8%85%E8%BE%89%E8%A6%81%E5%92%8C%E8%83%96%E4%B8%9C%E6%9D%A5%E6%AD%BB%E7%A3%95%E5%88%B0%E5%BA%95"
    },
    {
      "title": "TES真杀疯了连赛事导播也没放过",
      "desc": "TES对阵AL时导播给到一位举牌观众镜头，这名观众举着一个“首发就该赢替补”的牌子，暗指tarzan比kanavi强多了。TES官方下场，告知自家粉丝已经投诉了LPL导播。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/9358d109b3de9c82845dc0a12a81800a19d843e7.jpg?tbpicau=2025-04-19-05_28c674a8634c488ad740c977c3b87b28",
      "hot": 44380,
      "sort": null,
      "timestamp": "1743987063000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341117&amp;topic_name=TES%E7%9C%9F%E6%9D%80%E7%96%AF%E4%BA%86%E8%BF%9E%E8%B5%9B%E4%BA%8B%E5%AF%BC%E6%92%AD%E4%B9%9F%E6%B2%A1%E6%94%BE%E8%BF%87",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341117&amp;topic_name=TES%E7%9C%9F%E6%9D%80%E7%96%AF%E4%BA%86%E8%BF%9E%E8%B5%9B%E4%BA%8B%E5%AF%BC%E6%92%AD%E4%B9%9F%E6%B2%A1%E6%94%BE%E8%BF%87"
    },
    {
      "title": "U17国足连败出局主帅还能留吗",
      "desc": "U17亚洲杯小组赛A组第2轮，U17国足1-2不敌乌兹别克斯坦，提前出局，连续20年无缘世少赛。球迷指责主教练上村健一，球队没有战术体系，防守能力差，进攻无力。吧友们觉得这主帅还能留用吗？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/b7003af33a87e9503e2dcb4256385343fbf2b4e1.jpg?tbpicau=2025-04-19-05_81894c3e4dd896af7743b7c8ed195b61",
      "hot": 44979,
      "sort": null,
      "timestamp": "1743993089000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341119&amp;topic_name=U17%E5%9B%BD%E8%B6%B3%E8%BF%9E%E8%B4%A5%E5%87%BA%E5%B1%80%E4%B8%BB%E5%B8%85%E8%BF%98%E8%83%BD%E7%95%99%E5%90%97",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341119&amp;topic_name=U17%E5%9B%BD%E8%B6%B3%E8%BF%9E%E8%B4%A5%E5%87%BA%E5%B1%80%E4%B8%BB%E5%B8%85%E8%BF%98%E8%83%BD%E7%95%99%E5%90%97"
    },
    {
      "title": "总算见识到股灾是啥样了",
      "desc": "美股期货全线崩盘，国际油价暴跌，黄金跳水，关税阴云笼罩全球市场。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/e4dde71190ef76c69f9fc305db16fdfaaf51674a.jpg?tbpicau=2025-04-19-05_33fef3a28899c5c25a21220b71e624ca",
      "hot": 300751,
      "sort": null,
      "timestamp": "1743993970000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341122&amp;topic_name=%E6%80%BB%E7%AE%97%E8%A7%81%E8%AF%86%E5%88%B0%E8%82%A1%E7%81%BE%E6%98%AF%E5%95%A5%E6%A0%B7%E4%BA%86",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341122&amp;topic_name=%E6%80%BB%E7%AE%97%E8%A7%81%E8%AF%86%E5%88%B0%E8%82%A1%E7%81%BE%E6%98%AF%E5%95%A5%E6%A0%B7%E4%BA%86"
    },
    {
      "title": "曼联0-0闷平曼城",
      "desc": "英超第31轮焦点战，曼联主场迎战曼城。曼联多次制造良机，福登错失单刀，最终双方0-0战平。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/a5c27d1ed21b0ef49a69a0709bc451da81cb3e1e.jpg?tbpicau=2025-04-19-05_b4a988901942aeb6c11c29fbe3da73c6",
      "hot": 21777,
      "sort": null,
      "timestamp": "1743995659000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341123&amp;topic_name=%E6%9B%BC%E8%81%940-0%E9%97%B7%E5%B9%B3%E6%9B%BC%E5%9F%8E",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341123&amp;topic_name=%E6%9B%BC%E8%81%940-0%E9%97%B7%E5%B9%B3%E6%9B%BC%E5%9F%8E"
    },
    {
      "title": "下一任韩国总统会是谁",
      "desc": "据韩联社，韩国政府初步计划于6月3日举行新一届总统大选。你认为谁有希望成为尹锡悦的继任者？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/3812b31bb051f8193eea3b649cb44aed2f73e7d5.jpg?tbpicau=2025-04-19-05_29ca7a0d201551fbd648b09006a67905",
      "hot": 137168,
      "sort": null,
      "timestamp": "1743997505000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341124&amp;topic_name=%E4%B8%8B%E4%B8%80%E4%BB%BB%E9%9F%A9%E5%9B%BD%E6%80%BB%E7%BB%9F%E4%BC%9A%E6%98%AF%E8%B0%81",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341124&amp;topic_name=%E4%B8%8B%E4%B8%80%E4%BB%BB%E9%9F%A9%E5%9B%BD%E6%80%BB%E7%BB%9F%E4%BC%9A%E6%98%AF%E8%B0%81"
    },
    {
      "title": "美国杜鲁门号航母又挨揍了",
      "desc": "胡塞武装军事发言人发布声明，该武装在红海北部对敌方军舰实施交战行动，使用多枚巡航导弹和无人机，打击目标包括以美国“杜鲁门”号航母为首的多艘战舰。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/5ab5c9ea15ce36d3d06eb81e7cf33a87e950b1b1.jpg?tbpicau=2025-04-19-05_42ed01c2819d57b9eb2f340fc129731e",
      "hot": 74578,
      "sort": null,
      "timestamp": "1743998416000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341125&amp;topic_name=%E7%BE%8E%E5%9B%BD%E6%9D%9C%E9%B2%81%E9%97%A8%E5%8F%B7%E8%88%AA%E6%AF%8D%E5%8F%88%E6%8C%A8%E6%8F%8D%E4%BA%86",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341125&amp;topic_name=%E7%BE%8E%E5%9B%BD%E6%9D%9C%E9%B2%81%E9%97%A8%E5%8F%B7%E8%88%AA%E6%AF%8D%E5%8F%88%E6%8C%A8%E6%8F%8D%E4%BA%86"
    },
    {
      "title": "浪姐也没放过Mujica",
      "desc": "浪姐6播出李艺彤追番Mujica，刚好看到三角初音爬行名场面，锐评初音得精神病了。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/aa64034f78f0f7360d59762f4c55b319ebc4137c.jpg?tbpicau=2025-04-19-05_72754a0245eb8c6ae9f780d52aaad497",
      "hot": 62595,
      "sort": null,
      "timestamp": "1744004490000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341126&amp;topic_name=%E6%B5%AA%E5%A7%90%E4%B9%9F%E6%B2%A1%E6%94%BE%E8%BF%87Mujica",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341126&amp;topic_name=%E6%B5%AA%E5%A7%90%E4%B9%9F%E6%B2%A1%E6%94%BE%E8%BF%87Mujica"
    },
    {
      "title": "欧盟对美关税反制力度有多大",
      "desc": "据路透社，因美国对多国加征关税，欧盟准备对价值280亿美元的美国商品实施反制措施，品类涉及肉类、谷物、葡萄酒等。吧友们如何评价？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/f3d3572c11dfa9ec593a733924d0f703918fc18c.jpg?tbpicau=2025-04-19-05_c5336ce532273edbae51a67d9effd541",
      "hot": 346700,
      "sort": null,
      "timestamp": "1744008110000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341127&amp;topic_name=%E6%AC%A7%E7%9B%9F%E5%AF%B9%E7%BE%8E%E5%85%B3%E7%A8%8E%E5%8F%8D%E5%88%B6%E5%8A%9B%E5%BA%A6%E6%9C%89%E5%A4%9A%E5%A4%A7",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341127&amp;topic_name=%E6%AC%A7%E7%9B%9F%E5%AF%B9%E7%BE%8E%E5%85%B3%E7%A8%8E%E5%8F%8D%E5%88%B6%E5%8A%9B%E5%BA%A6%E6%9C%89%E5%A4%9A%E5%A4%A7"
    },
    {
      "title": "药大女生借衣不还吧友发贴讨公道",
      "desc": "中国药科大学一女生借走吧友的大衣出cos，将近3年未归还，面对吧友催促态度恶劣。大衣对吧友有特殊意义，数次追讨无果，无奈之下发贴求助，大伙速来支招！",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/d53f8794a4c27d1ea9d21fea5dd5ad6eddc43823.jpg?tbpicau=2025-04-19-05_2100fe30b55a109db8eb950e1aa4660c",
      "hot": 1661202,
      "sort": null,
      "timestamp": "1744014794000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341130&amp;topic_name=%E8%8D%AF%E5%A4%A7%E5%A5%B3%E7%94%9F%E5%80%9F%E8%A1%A3%E4%B8%8D%E8%BF%98%E5%90%A7%E5%8F%8B%E5%8F%91%E8%B4%B4%E8%AE%A8%E5%85%AC%E9%81%93",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341130&amp;topic_name=%E8%8D%AF%E5%A4%A7%E5%A5%B3%E7%94%9F%E5%80%9F%E8%A1%A3%E4%B8%8D%E8%BF%98%E5%90%A7%E5%8F%8B%E5%8F%91%E8%B4%B4%E8%AE%A8%E5%85%AC%E9%81%93"
    },
    {
      "title": "明日方舟M3强度什么杯",
      "desc": "明日方舟Mon3tr解包技能数值来了，强度怎么样？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/aa18972bd40735fa6c9f6997d8510fb30f2408b4.jpg?tbpicau=2025-04-19-05_b789496dc227fd847fbf6df85ffb0fa0",
      "hot": 618838,
      "sort": null,
      "timestamp": "1744016441000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341131&amp;topic_name=%E6%98%8E%E6%97%A5%E6%96%B9%E8%88%9FM3%E5%BC%BA%E5%BA%A6%E4%BB%80%E4%B9%88%E6%9D%AF",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341131&amp;topic_name=%E6%98%8E%E6%97%A5%E6%96%B9%E8%88%9FM3%E5%BC%BA%E5%BA%A6%E4%BB%80%E4%B9%88%E6%9D%AF"
    },
    {
      "title": "湘大宿舍投毒案凶手一审死刑",
      "desc": "湘潭大学宿舍投毒案一审宣判，凶手周立人因故意杀人罪被判死刑。2024年4月3日，周立人向寝室内张某某的罐装麦片内投放秋水仙碱粉末。4月7日，张某某食用该麦片后出现呕吐等症状，并到医院就诊。4月13日，张某某经抢救无效死亡。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/71cf3bc79f3df8dcbba1a8bf8b11728b4710287b.jpg?tbpicau=2025-04-19-05_b9d67b315027723b8d3d4777c6d63e86",
      "hot": 102915,
      "sort": null,
      "timestamp": "1744017621000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341132&amp;topic_name=%E6%B9%98%E5%A4%A7%E5%AE%BF%E8%88%8D%E6%8A%95%E6%AF%92%E6%A1%88%E5%87%B6%E6%89%8B%E4%B8%80%E5%AE%A1%E6%AD%BB%E5%88%91",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341132&amp;topic_name=%E6%B9%98%E5%A4%A7%E5%AE%BF%E8%88%8D%E6%8A%95%E6%AF%92%E6%A1%88%E5%87%B6%E6%89%8B%E4%B8%80%E5%AE%A1%E6%AD%BB%E5%88%91"
    },
    {
      "title": "211女会和大专男谈恋爱吗",
      "desc": "跟211学历的女朋友坦白我是大专生，她说不介意，吧友们觉得她是真心的吗？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/a9d3fd1f4134970a6cf03601d3cad1c8a7865d18.jpg?tbpicau=2025-04-19-05_c8f36e76ae433537c0c6b0f0f083c960",
      "hot": 841776,
      "sort": null,
      "timestamp": "1744023267000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341133&amp;topic_name=211%E5%A5%B3%E4%BC%9A%E5%92%8C%E5%A4%A7%E4%B8%93%E7%94%B7%E8%B0%88%E6%81%8B%E7%88%B1%E5%90%97",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341133&amp;topic_name=211%E5%A5%B3%E4%BC%9A%E5%92%8C%E5%A4%A7%E4%B8%93%E7%94%B7%E8%B0%88%E6%81%8B%E7%88%B1%E5%90%97"
    },
    {
      "title": "甲亢哥在长沙被一颗槟榔单杀",
      "desc": "甲亢哥长沙直播遇“槟榔暴击”，抽象顶流被粉丝投喂整懵，表情管理彻底失控。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/6609c93d70cf3bc7f8c7ebc29700baa1cc112adc.jpg?tbpicau=2025-04-19-05_89f5cbc657961b10b8957bcbb5dac5ed",
      "hot": 748788,
      "sort": null,
      "timestamp": "1744024020000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341134&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E5%9C%A8%E9%95%BF%E6%B2%99%E8%A2%AB%E4%B8%80%E9%A2%97%E6%A7%9F%E6%A6%94%E5%8D%95%E6%9D%80",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341134&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E5%9C%A8%E9%95%BF%E6%B2%99%E8%A2%AB%E4%B8%80%E9%A2%97%E6%A7%9F%E6%A6%94%E5%8D%95%E6%9D%80"
    },
    {
      "title": "48岁马布里与女歌手汪妤凌再婚",
      "desc": "48岁的前NBA球星布里发视频官宣，与中国歌手汪妤凌登记结婚，自己将成为中国女婿。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/91ef76c6a7efce1baeaae9e4e951f3deb48f651e.jpg?tbpicau=2025-04-19-05_d4bc56186acde28f1de1cf3d48eec412",
      "hot": 439887,
      "sort": null,
      "timestamp": "1744027550000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341135&amp;topic_name=48%E5%B2%81%E9%A9%AC%E5%B8%83%E9%87%8C%E4%B8%8E%E5%A5%B3%E6%AD%8C%E6%89%8B%E6%B1%AA%E5%A6%A4%E5%87%8C%E5%86%8D%E5%A9%9A",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341135&amp;topic_name=48%E5%B2%81%E9%A9%AC%E5%B8%83%E9%87%8C%E4%B8%8E%E5%A5%B3%E6%AD%8C%E6%89%8B%E6%B1%AA%E5%A6%A4%E5%87%8C%E5%86%8D%E5%A9%9A"
    },
    {
      "title": "甲亢哥大张伟合唱阳光彩虹小白马",
      "desc": "甲亢哥与大张伟终于在长沙相见，众人齐唱主题曲《阳光彩虹小白马》，还被路人投喂肯德基。大家如何评价这场直播？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/ac4bd11373f08202ce55b1110dfbfbedaa641bdc.jpg?tbpicau=2025-04-19-05_5df592f4ff52395c32279ce9a55cb9d0",
      "hot": 2731017,
      "sort": null,
      "timestamp": "1744033583000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341137&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E5%A4%A7%E5%BC%A0%E4%BC%9F%E5%90%88%E5%94%B1%E9%98%B3%E5%85%89%E5%BD%A9%E8%99%B9%E5%B0%8F%E7%99%BD%E9%A9%AC",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341137&amp;topic_name=%E7%94%B2%E4%BA%A2%E5%93%A5%E5%A4%A7%E5%BC%A0%E4%BC%9F%E5%90%88%E5%94%B1%E9%98%B3%E5%85%89%E5%BD%A9%E8%99%B9%E5%B0%8F%E7%99%BD%E9%A9%AC"
    },
    {
      "title": "蔡徐坤鸽鸽的官司打赢了",
      "desc": "蔡徐坤工作室发布声明，宣布针对账号“懂瓜呱”等网络造谣者的名誉权纠纷案终审胜诉。本次胜诉相关赔偿款将通过公益项目捐出。吧友们以后都别乱发表情包了！",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/4b90f603738da97792aea505f651f8198618e31f.jpg?tbpicau=2025-04-19-05_20f9ee9c34dfe065ba6d4707334d31ef",
      "hot": 193885,
      "sort": null,
      "timestamp": "1744037347000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341138&amp;topic_name=%E8%94%A1%E5%BE%90%E5%9D%A4%E9%B8%BD%E9%B8%BD%E7%9A%84%E5%AE%98%E5%8F%B8%E6%89%93%E8%B5%A2%E4%BA%86",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341138&amp;topic_name=%E8%94%A1%E5%BE%90%E5%9D%A4%E9%B8%BD%E9%B8%BD%E7%9A%84%E5%AE%98%E5%8F%B8%E6%89%93%E8%B5%A2%E4%BA%86"
    },
    {
      "title": "登峰组还有谁！TES继续大杀四方",
      "desc": "TES2比1战胜WBG，决胜局jkl掏出德莱文砍翻WBG。登峰组究竟还有谁能抽TES的陀螺？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/d1160924ab18972b574722e4a0cd7b899e510a9a.jpg?tbpicau=2025-04-19-05_572e1cb4d9ccc5e65227ae7425c3c433",
      "hot": 1059150,
      "sort": null,
      "timestamp": "1744039826000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341139&amp;topic_name=%E7%99%BB%E5%B3%B0%E7%BB%84%E8%BF%98%E6%9C%89%E8%B0%81%EF%BC%81TES%E7%BB%A7%E7%BB%AD%E5%A4%A7%E6%9D%80%E5%9B%9B%E6%96%B9",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341139&amp;topic_name=%E7%99%BB%E5%B3%B0%E7%BB%84%E8%BF%98%E6%9C%89%E8%B0%81%EF%BC%81TES%E7%BB%A7%E7%BB%AD%E5%A4%A7%E6%9D%80%E5%9B%9B%E6%96%B9"
    },
    {
      "title": "懂王破防威胁要向中国加50%关税",
      "desc": "特朗普发文称，如果中国不在8日前取消34%的关税，将从9日起，继续对中国产品额外征收50%的关税。中国驻美大使回应，中国将坚定维护自身合法权益。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/d4628535e5dde711b8c66dd8e1efce1b9d166183.jpg?tbpicau=2025-04-19-05_bc5bdb90c13000c22783e47d6c5c7355",
      "hot": 2734080,
      "sort": null,
      "timestamp": "1744073586000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341140&amp;topic_name=%E6%87%82%E7%8E%8B%E7%A0%B4%E9%98%B2%E5%A8%81%E8%83%81%E8%A6%81%E5%90%91%E4%B8%AD%E5%9B%BD%E5%8A%A050%25%E5%85%B3%E7%A8%8E",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341140&amp;topic_name=%E6%87%82%E7%8E%8B%E7%A0%B4%E9%98%B2%E5%A8%81%E8%83%81%E8%A6%81%E5%90%91%E4%B8%AD%E5%9B%BD%E5%8A%A050%25%E5%85%B3%E7%A8%8E"
    },
    {
      "title": "美副总统说中国人是“乡巴佬”",
      "desc": "万斯在媒体前讨论经济全球化时称，美国向中国“乡巴佬”借钱，来购买中国“乡巴佬”制造的东西。公然把中国人称作“乡巴佬”。吧友们如何评价副总统万斯这一歧视性用词？",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/43a7d933c895d14327d8ca0d35f082025aaf0775.jpg?tbpicau=2025-04-19-05_04dfc0edcd71e1d252679bd0564079de",
      "hot": 1194336,
      "sort": null,
      "timestamp": "1744075621000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341141&amp;topic_name=%E7%BE%8E%E5%89%AF%E6%80%BB%E7%BB%9F%E8%AF%B4%E4%B8%AD%E5%9B%BD%E4%BA%BA%E6%98%AF%E2%80%9C%E4%B9%A1%E5%B7%B4%E4%BD%AC%E2%80%9D",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341141&amp;topic_name=%E7%BE%8E%E5%89%AF%E6%80%BB%E7%BB%9F%E8%AF%B4%E4%B8%AD%E5%9B%BD%E4%BA%BA%E6%98%AF%E2%80%9C%E4%B9%A1%E5%B7%B4%E4%BD%AC%E2%80%9D"
    },
    {
      "title": "马斯克也扛不住特朗普关税",
      "desc": "马斯克通过社交平台和公开场合表达对特朗普关税政策的不满，似乎要与特朗普划清界限。据报道，特朗普关税新措施引发全球市场暴跌，马斯克的净资产缩水310亿美元，今年以来的总损失达1300亿美元。",
      "cover": "https://tiebapic.baidu.com/forum/pic/item/d1160924ab18972babdb2ee4a0cd7b899e510a3e.jpg?tbpicau=2025-04-19-05_acf4d521029b85bd2ff599142b7a6844",
      "hot": 2232104,
      "sort": null,
      "timestamp": "1744078014000",
      "formatTime": null,
      "mobileUrl": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341142&amp;topic_name=%E9%A9%AC%E6%96%AF%E5%85%8B%E4%B9%9F%E6%89%9B%E4%B8%8D%E4%BD%8F%E7%89%B9%E6%9C%97%E6%99%AE%E5%85%B3%E7%A8%8E",
      "url": "https://tieba.baidu.com/hottopic/browse/hottopic?topic_id=28341142&amp;topic_name=%E9%A9%AC%E6%96%AF%E5%85%8B%E4%B9%9F%E6%89%9B%E4%B8%8D%E4%BD%8F%E7%89%B9%E6%9C%97%E6%99%AE%E5%85%B3%E7%A8%8E"
    }
  ],
  "ok": true
}
```

- `wx/hot/getNewsBanner` - 获取新闻轮播图
```json
{
  "code": 200,
  "msg": "SUCCESS",
  "data": [
    "https://imgpai.thepaper.cn/newpai/image/1743990864947_c4Cr8P_1743990865081.png",
    "https://img.36krcdn.com/hsossms/20250407/v2_697a4a3a76cc499486ddd77407bf2db2@1743780481_oswg972080oswg1053oswg495_img_png?x-oss-process=image/resize,m_mfit,w_600,h_400,limit_0/crop,w_600,h_400,g_center",
    "https://static.gametalk.qq.com/image/3/1741329307_7287f9e76b82ef42acbec1221e51ee30.png",
    "https://tiebapic.baidu.com/forum/pic/item/cc11728b4710b9124d873ed385fdfc039245225d.jpg?tbpicau=2025-04-19-05_09f5a780f8e24c031d4272997cdf55e1"
  ],
  "ok": true
}
```

### 文件存储相关
- `minio/upload` - 上传文件 直接贴后端代码，目的是存入minio文件服务中进行储存，数据库只存关联信息
```java
/**
 * 上传文件
 *
 * @param file   文件
 * @param folder 文件夹
 * @return R
 */
@SysLog("文件预览功能调用")
@IgnoreUri
@RedisIncrement(key = FILE_UPLOAD_USER)
@Operation(description = "上传文件", summary = "上传文件")
@PostMapping("/upload")
public R<Map<String, Object>> upload(@RequestParam("file") MultipartFile file, String folder) {
    folder = Optional.ofNullable(folder).orElse("preview/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    String fileName = file.getOriginalFilename();
    String fileType = FileUtil.getFileType(fileName);
    String uuName = IdUtil.generate32BitShortId() + StringPool.DOT + fileType;
    // 文件夹不存在创建
    Map<String, Object> resultMap = new HashMap<>(4);
    resultMap.put("fileName", fileName);

    try {
        // 计算文件的 MD5 值
        String md5 = FileUtil.calculateMd5(file);

        // 检查文件是否已经存在
        Optional<SysFileEntity> existingFile = sysFileService.getOneOpt(Wrappers.<SysFileEntity>query().lambda().eq(SysFileEntity::getMd5, md5));
        if (existingFile.isPresent()) {
            // 文件已存在，返回已有文件的信息
            SysFileEntity sysFile = existingFile.get();
            resultMap.put("uuName", sysFile.getFileName());
            String url = ossTemplate.getObjectURL(sysFile.getBucketName(), sysFile.getFileName(), Duration.ofHours(2));
            resultMap.put("url", url);
            return R.ok(resultMap);
        }

        ossTemplate.putObject(folder, uuName, file.getInputStream());
        resultMap.put("uuName", uuName);
        // 同时获取带时间限制的文件链接
        String url = ossTemplate.getObjectURL(folder, uuName, Duration.ofHours(2));
        resultMap.put("url", url);

        long fileSize = file.getSize();
        sysFileService.saveFileInfo(uuName, folder, fileName, fileType, fileSize, md5);
    } catch (Exception e) {
        throw new FileException(e.getLocalizedMessage());
    }
    return R.ok(resultMap);
}
```

- `minio/remove-bg` - 图片去底 直接贴代码，实现方式为去除背景传至minio服务中，然后获取minio的临时访问链接
```java
/**
 * 移除图片背景
 *
 * @param file 文件
 * @return R
 */
@SysLog("移除图片背景功能调用")
@IgnoreUri
@RedisIncrement(key = FILE_UPLOAD_USER)
@Operation(description = "移除图片背景", summary = "移除图片背景")
@PostMapping(value = "/remove-bg", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
public R<String> removeImageBg(@RequestParam("file") MultipartFile file) {
    // 动态设置文件名，如果上传的文件有名字
    String fileName = file.getOriginalFilename() != null ? file.getOriginalFilename() : "image";
    byte[] data;
    try {
        // 将上传的文件转换为字节数组
        data = HttpRequest
                .post(pythonProperties.getRmbgUrl())
                .multipartFormBuilder().add("file", fileName, file.getInputStream())
                .build().readTimeout(Duration.of(60, TimeUnit.SECONDS.toChronoUnit()))
                .execute()
                .asBytes();

        // 上传处理后的图片到Minio
        InputStream inputStream = new ByteArrayInputStream(data);
        String outputObjectName = "output_" + fileName;
        ossTemplate.putObject(pythonProperties.getBucket(), outputObjectName, inputStream);

        // 同时获取带时间限制的文件链接
        String fileUrl = ossTemplate.getObjectURL(pythonProperties.getBucket(), outputObjectName, Duration.ofHours(2));

        return R.ok(fileUrl, "文件上传并处理成功");
    } catch (Exception e) {
        log.error("Python服务未启动或者图片去除背景失败{}", e.getLocalizedMessage());
        return R.fail("Python服务未启动或者图片去除背景失败");
    }
}
```

### 用户数据
`wx/statics/profileStatics` - 获取用户统计数据
```json
{
  "code": 200,
  "msg": "SUCCESS",
  "data": {
    "fc": 239,
    "sc": 56,
    "tc": 6,
    "version": "1.3.0"
  },
  "ok": true
}
```

### 第三方服务
- `nbnhhsh/guess` - 缩略语翻译 直接贴接口
```java
import cn.allbs.admin.config.core.R;
import cn.allbs.admin.config.http.HttpRequest;
import cn.allbs.admin.config.properties.UrlProperties;
import cn.allbs.admin.dto.wx.AcronymTransDTO;
import cn.allbs.admin.security.annotation.IgnoreUri;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 类 ThirdPartController
 *
 * <AUTHOR>
 * &#064;date 2024/8/30
 */
@Slf4j
@IgnoreUri
@RestController
@RequestMapping("nbnhhsh")
@RequiredArgsConstructor
@Tag(name = "第三方接口请求", description = "第三方接口请求")
public class ThirdPartController {

    private final UrlProperties urlProperties;

    /**
     * 获取首字母缩写翻译
     */
    @PostMapping("guess")
    public R<?> getAcronymTranslation(@Valid @RequestBody AcronymTransDTO acronymTransDTO) {
        JsonNode node = HttpRequest.post("https://lab.magiconch.com/api/nbnhhsh/guess")
                .addHeader("Content-Type", "application/json")
                .bodyJson(acronymTransDTO)
                .execute()
                .asJsonNode();
        return R.ok(node);
    }
}
```

## 小程序展示

### 首页（工具箱）
![](https://img.tuytuy.com/2025/04/7ab2467623b49c52839f98d5957f6963.png)
![](https://img.tuytuy.com/2025/04/754ccca83deb960aaae1eeac96a2e34a.png)
![](https://img.tuytuy.com/2025/04/844f28a2a02aa497ef6afe1f9f0d5393.png)
![](https://img.tuytuy.com/2025/04/0d3c7c411450b1e6f48330e26b5a8185.png)

### 热点新闻页（需企业认证）
![](https://img.tuytuy.com/2025/04/5a4ccea592f969d1969540f6ca9e610c.png)


### 证件照制作
![](https://img.tuytuy.com/2025/04/b0ba46f511936a428968e8523b5cf530.png)
![](https://img.tuytuy.com/2025/04/d34768ff03a7b77e8c422ef057848c5d.png)


### 个人信息
![](https://img.tuytuy.com/2025/04/3bc3a5e5bd1e5a60aca4cf421bda3e55.png)

