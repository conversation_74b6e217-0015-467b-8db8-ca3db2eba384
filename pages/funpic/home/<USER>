<view class="funpic-container">
    <!-- 固定头部区域 -->
    <view class="fixed-header">
        <!-- 头部标题区域 -->
        <view class="funpic-header">
            <view class="header-title">🖼️ 趣图大全</view>
            <view class="header-subtitle">精选有趣图片，带给你欢乐时光</view>
        </view>

        <!-- 分类筛选区域 -->
        <view class="category-filter">
            <scroll-view class="category-scroll" scroll-x="true">
                <view class="category-list">
                    <view wx:for="{{categories}}" wx:key="*this"
                          class="category-item {{selectedCategory === item ? 'active' : ''}}"
                          bindtap="switchCategory" data-category="{{item}}">
                        {{item}}
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view class="scroll-content" scroll-y="true" enhanced="true" show-scrollbar="false">
        <!-- 瀑布流图片区域 -->
        <view class="waterfall-container">
            <!-- 左列 -->
            <view class="waterfall-column">
                <view wx:for="{{leftColumnPics}}" wx:key="id"
                      class="pic-item" bindtap="viewImage"
                      data-index="{{index}}" data-column="left">
                    <image class="pic-image"
                           src="{{item.url}}"
                           mode="widthFix"
                           lazy-load="true"
                           binderror="onImageError"
                           bindload="onImageLoad"></image>
                    <view class="pic-overlay">
                        <view class="pic-title">{{item.title}}</view>
                        <view class="pic-category">{{item.category}}</view>
                    </view>
                </view>
            </view>

            <!-- 右列 -->
            <view class="waterfall-column">
                <view wx:for="{{rightColumnPics}}" wx:key="id"
                      class="pic-item" bindtap="viewImage"
                      data-index="{{index}}" data-column="right">
                    <image class="pic-image"
                           src="{{item.url}}"
                           mode="widthFix"
                           lazy-load="true"
                           binderror="onImageError"
                           bindload="onImageLoad"></image>
                    <view class="pic-overlay">
                        <view class="pic-title">{{item.title}}</view>
                        <view class="pic-category">{{item.category}}</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部间距 -->
        <view class='cu-tabbar-height'></view>
    </scroll-view>

    <!-- 图片查看器 -->
    <view wx:if="{{showImageViewer}}" class="image-viewer" bindtap="closeImageViewer">
        <view class="viewer-content" catchtap="">
            <image class="viewer-image"
                   src="{{filteredPics[currentImageIndex].url}}"
                   mode="aspectFit"
                   binderror="onImageError"
                   bindload="onImageLoad"></image>
            <view class="viewer-info">
                <view class="viewer-title">{{filteredPics[currentImageIndex].title}}</view>
                <view class="viewer-desc">{{filteredPics[currentImageIndex].description}}</view>
            </view>
            <view class="viewer-actions">
                <button class="save-btn" bindtap="saveImage">
                    保存到相册
                </button>
            </view>
        </view>
    </view>
</view>
