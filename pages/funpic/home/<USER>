/* pages/funpic/home/<USER>/
.funpic-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

/* 固定头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    padding: 40rpx 30rpx 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

/* 头部区域 */
.funpic-header {
    text-align: center;
    margin-bottom: 30rpx;
    padding: 20rpx 0;
}

.header-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 16rpx;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.header-subtitle {
    font-size: 26rpx;
    color: rgba(255,255,255,0.9);
    line-height: 1.5;
}

/* 分类筛选区域 */
.category-filter {
    margin-bottom: 20rpx;
}

.category-scroll {
    white-space: nowrap;
}

.category-list {
    display: flex;
    padding: 0 10rpx;
}

.category-item {
    flex-shrink: 0;
    padding: 14rpx 28rpx;
    margin-right: 16rpx;
    background: rgba(255,255,255,0.3);
    border-radius: 50rpx;
    font-size: 26rpx;
    color: #fff;
    text-align: center;
    transition: all 0.3s ease;
}

.category-item.active {
    background: rgba(255,255,255,0.9);
    color: #ff6b9d;
    font-weight: bold;
    transform: scale(1.05);
}

/* 可滚动内容区域 */
.scroll-content {
    flex: 1;
    margin-top: 300rpx; /* 增加间距，避免遮挡 */
    padding: 30rpx 15rpx 0; /* 增加顶部内边距 */
}

/* 瀑布流容器 */
.waterfall-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

/* 瀑布流列 */
.waterfall-column {
    width: 48%;
    display: flex;
    flex-direction: column;
}

/* 图片项 */
.pic-item {
    margin-bottom: 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 6rpx 24rpx rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    break-inside: avoid; /* 防止瀑布流断裂 */
}

.pic-item:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.12);
}

.pic-image {
    width: 100%;
    height: auto; /* 自适应高度 */
    display: block;
    border-radius: 16rpx 16rpx 0 0;
}

.pic-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 30rpx 16rpx 16rpx;
    color: #fff;
}

.pic-title {
    font-size: 26rpx;
    font-weight: bold;
    margin-bottom: 6rpx;
    text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.5);
    line-height: 1.3;
}

.pic-category {
    font-size: 20rpx;
    opacity: 0.9;
    background: rgba(255,255,255,0.2);
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    display: inline-block;
}

/* 图片查看器 */
.image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.95);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-content {
    width: 90%;
    max-width: 600rpx;
    text-align: center;
}

.viewer-image {
    width: 100%;
    max-height: 70vh;
    border-radius: 16rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.3);
}

.viewer-info {
    color: #fff;
    margin-bottom: 40rpx;
    padding: 0 20rpx;
}

.viewer-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
    line-height: 1.4;
}

.viewer-desc {
    font-size: 26rpx;
    opacity: 0.8;
    line-height: 1.5;
}

.viewer-actions {
    display: flex;
    justify-content: center;
}

.save-btn {
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    color: #fff;
    border: none;
    border-radius: 50rpx;
    padding: 18rpx 50rpx;
    font-size: 26rpx;
    font-weight: bold;
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.3);
    transition: all 0.3s ease;
}

.save-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.4);
}
