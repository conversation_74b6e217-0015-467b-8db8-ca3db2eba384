// pages/funpic/home/<USER>
Component({
    options: {
        addGlobalClass: true,
    },
    data: {
        currentImageIndex: 0,
        showImageViewer: false,
        funPics: [
            {
                id: 1,
                title: '可爱小猫咪',
                url: 'https://picsum.photos/400/400?random=1',
                description: '超级可爱的小猫咪，萌化你的心~',
                category: '萌宠'
            },
            {
                id: 2,
                title: '搞笑表情包',
                url: 'https://picsum.photos/400/400?random=2',
                description: '让你笑到停不下来的表情包',
                category: '搞笑'
            },
            {
                id: 3,
                title: '美丽风景',
                url: 'https://picsum.photos/400/400?random=3',
                description: '令人心旷神怡的自然风光',
                category: '风景'
            },
            {
                id: 4,
                title: '创意设计',
                url: 'https://picsum.photos/400/400?random=4',
                description: '脑洞大开的创意设计作品',
                category: '创意'
            },
            {
                id: 5,
                title: '治愈系插画',
                url: 'https://picsum.photos/400/400?random=5',
                description: '温暖治愈的手绘插画',
                category: '插画'
            },
            {
                id: 6,
                title: '奇趣动物',
                url: 'https://picsum.photos/400/400?random=6',
                description: '动物世界的奇趣瞬间',
                category: '动物'
            },
            {
                id: 7,
                title: '梦幻星空',
                url: 'https://picsum.photos/400/400?random=7',
                description: '浩瀚星空下的无限遐想',
                category: '风景'
            },
            {
                id: 8,
                title: '萌萌哒小狗',
                url: 'https://picsum.photos/400/400?random=8',
                description: '忠诚可爱的小伙伴',
                category: '萌宠'
            },
            {
                id: 9,
                title: '抽象艺术',
                url: 'https://picsum.photos/400/400?random=9',
                description: '充满想象力的抽象作品',
                category: '创意'
            },
            {
                id: 10,
                title: '温馨插画',
                url: 'https://picsum.photos/400/400?random=10',
                description: '温暖人心的手绘作品',
                category: '插画'
            }
        ],
        categories: ['全部', '萌宠', '搞笑', '风景', '创意', '插画', '动物'],
        selectedCategory: '全部'
    },
    methods: {
        // 查看大图
        viewImage(e) {
            const index = e.currentTarget.dataset.index;
            const filteredPics = this.getFilteredPics();
            this.setData({
                currentImageIndex: index,
                showImageViewer: true
            });
        },

        // 关闭图片查看器
        closeImageViewer() {
            this.setData({
                showImageViewer: false
            });
        },

        // 切换分类
        switchCategory(e) {
            const category = e.currentTarget.dataset.category;
            this.setData({
                selectedCategory: category
            });
        },

        // 获取过滤后的图片
        getFilteredPics() {
            if (this.data.selectedCategory === '全部') {
                return this.data.funPics;
            }
            return this.data.funPics.filter(pic => pic.category === this.data.selectedCategory);
        },

        // 保存图片到相册
        saveImage(e) {
            const url = e.currentTarget.dataset.url;
            wx.showLoading({
                title: '保存中...'
            });

            wx.downloadFile({
                url: url,
                success: (res) => {
                    wx.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath,
                        success: () => {
                            wx.hideLoading();
                            wx.showToast({
                                title: '保存成功',
                                icon: 'success'
                            });
                        },
                        fail: () => {
                            wx.hideLoading();
                            wx.showToast({
                                title: '保存失败',
                                icon: 'none'
                            });
                        }
                    });
                },
                fail: () => {
                    wx.hideLoading();
                    wx.showToast({
                        title: '下载失败',
                        icon: 'none'
                    });
                }
            });
        }
    },
})
