// pages/funpic/home/<USER>
Component({
    options: {
        addGlobalClass: true,
    },
    data: {
        currentImageIndex: 0,
        showImageViewer: false,
        funPics: [
            {
                id: 1,
                title: '可爱小猫咪',
                url: 'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=300&h=400&fit=crop',
                description: '超级可爱的小猫咪，萌化你的心~',
                category: '萌宠',
                height: 400
            },
            {
                id: 2,
                title: '搞笑表情包',
                url: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=350&fit=crop',
                description: '让你笑到停不下来的表情包',
                category: '搞笑',
                height: 350
            },
            {
                id: 3,
                title: '美丽风景',
                url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=500&fit=crop',
                description: '令人心旷神怡的自然风光',
                category: '风景',
                height: 500
            },
            {
                id: 4,
                title: '创意设计',
                url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=320&fit=crop',
                description: '脑洞大开的创意设计作品',
                category: '创意',
                height: 320
            },
            {
                id: 5,
                title: '治愈系插画',
                url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=450&fit=crop',
                description: '温暖治愈的手绘插画',
                category: '插画',
                height: 450
            },
            {
                id: 6,
                title: '奇趣动物',
                url: 'https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=300&h=380&fit=crop',
                description: '动物世界的奇趣瞬间',
                category: '动物',
                height: 380
            },
            {
                id: 7,
                title: '梦幻星空',
                url: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=300&h=420&fit=crop',
                description: '浩瀚星空下的无限遐想',
                category: '风景',
                height: 420
            },
            {
                id: 8,
                title: '萌萌哒小狗',
                url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=300&h=360&fit=crop',
                description: '忠诚可爱的小伙伴',
                category: '萌宠',
                height: 360
            },
            {
                id: 9,
                title: '抽象艺术',
                url: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=480&fit=crop',
                description: '充满想象力的抽象作品',
                category: '创意',
                height: 480
            },
            {
                id: 10,
                title: '温馨插画',
                url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=340&fit=crop',
                description: '温暖人心的手绘作品',
                category: '插画',
                height: 340
            },
            {
                id: 11,
                title: '城市夜景',
                url: 'https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=300&h=520&fit=crop',
                description: '繁华都市的璀璨夜色',
                category: '风景',
                height: 520
            },
            {
                id: 12,
                title: '可爱兔子',
                url: 'https://images.unsplash.com/photo-1585110396000-c9ffd4e4b308?w=300&h=300&fit=crop',
                description: '软萌可爱的小兔子',
                category: '萌宠',
                height: 300
            },
            {
                id: 13,
                title: '搞怪表情',
                url: 'https://images.unsplash.com/photo-1574158622682-e40e69881006?w=300&h=390&fit=crop',
                description: '让人忍俊不禁的搞怪表情',
                category: '搞笑',
                height: 390
            },
            {
                id: 14,
                title: '艺术涂鸦',
                url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=460&fit=crop',
                description: '充满创意的街头涂鸦艺术',
                category: '创意',
                height: 460
            },
            {
                id: 15,
                title: '温暖插图',
                url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=410&fit=crop',
                description: '温馨治愈的手绘插图',
                category: '插画',
                height: 410
            }
        ],
        categories: ['全部', '萌宠', '搞笑', '风景', '创意', '插画', '动物'],
        selectedCategory: '全部',
        filteredPics: [], // 过滤后的图片列表
        leftColumnPics: [], // 左列图片
        rightColumnPics: [], // 右列图片
        leftColumnHeight: 0, // 左列高度
        rightColumnHeight: 0 // 右列高度
    },

    attached() {
        // 组件初始化时设置过滤后的图片
        this.updateFilteredPics();
    },
    methods: {
        // 更新过滤后的图片列表
        updateFilteredPics() {
            const filteredPics = this.getFilteredPics();
            this.setData({
                filteredPics: filteredPics
            }, () => {
                // 更新瀑布流布局
                this.updateWaterfallLayout();
            });
        },

        // 更新瀑布流布局
        updateWaterfallLayout() {
            const leftColumn = [];
            const rightColumn = [];
            let leftHeight = 0;
            let rightHeight = 0;

            this.data.filteredPics.forEach(pic => {
                // 计算图片在rpx单位下的高度（基于300rpx宽度）
                const picHeight = (pic.height * 300) / 300 + 100; // 100rpx为额外的padding和文字高度

                if (leftHeight <= rightHeight) {
                    leftColumn.push(pic);
                    leftHeight += picHeight;
                } else {
                    rightColumn.push(pic);
                    rightHeight += picHeight;
                }
            });

            this.setData({
                leftColumnPics: leftColumn,
                rightColumnPics: rightColumn,
                leftColumnHeight: leftHeight,
                rightColumnHeight: rightHeight
            });
        },

        // 查看大图
        viewImage(e) {
            const { index, column } = e.currentTarget.dataset;
            let actualIndex = 0;

            if (column === 'left') {
                const pic = this.data.leftColumnPics[index];
                actualIndex = this.data.filteredPics.findIndex(item => item.id === pic.id);
            } else {
                const pic = this.data.rightColumnPics[index];
                actualIndex = this.data.filteredPics.findIndex(item => item.id === pic.id);
            }

            this.setData({
                currentImageIndex: actualIndex,
                showImageViewer: true
            });
        },

        // 关闭图片查看器
        closeImageViewer() {
            this.setData({
                showImageViewer: false
            });
        },

        // 切换分类
        switchCategory(e) {
            const category = e.currentTarget.dataset.category;
            this.setData({
                selectedCategory: category
            }, () => {
                // 切换分类后更新过滤后的图片列表
                this.updateFilteredPics();
            });
        },

        // 获取过滤后的图片
        getFilteredPics() {
            if (this.data.selectedCategory === '全部') {
                return this.data.funPics;
            }
            return this.data.funPics.filter(pic => pic.category === this.data.selectedCategory);
        },

        // 保存图片到相册
        saveImage() {
            const currentPic = this.data.filteredPics[this.data.currentImageIndex];
            if (!currentPic) return;

            wx.showLoading({
                title: '保存中...'
            });

            wx.downloadFile({
                url: currentPic.url,
                success: (res) => {
                    wx.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath,
                        success: () => {
                            wx.hideLoading();
                            wx.showToast({
                                title: '保存成功',
                                icon: 'success'
                            });
                        },
                        fail: () => {
                            wx.hideLoading();
                            wx.showToast({
                                title: '保存失败',
                                icon: 'none'
                            });
                        }
                    });
                },
                fail: () => {
                    wx.hideLoading();
                    wx.showToast({
                        title: '下载失败',
                        icon: 'none'
                    });
                }
            });
        }
    },
})
