<view class="funpic-container">
    <!-- 头部标题区域 -->
    <view class="funpic-header">
        <view class="header-title">🖼️ 趣图大全</view>
        <view class="header-subtitle">精选有趣图片，带给你欢乐时光</view>
    </view>

    <!-- 分类筛选区域 -->
    <view class="category-filter">
        <scroll-view class="category-scroll" scroll-x="true">
            <view class="category-list">
                <view wx:for="{{categories}}" wx:key="*this"
                      class="category-item {{selectedCategory === item ? 'active' : ''}}"
                      bindtap="switchCategory" data-category="{{item}}">
                    {{item}}
                </view>
            </view>
        </scroll-view>
    </view>

    <!-- 图片网格区域 -->
    <view class="pic-grid">
        <view wx:for="{{selectedCategory === '全部' ? funPics : funPics.filter(pic => pic.category === selectedCategory)}}"
              wx:key="id" class="pic-item" bindtap="viewImage" data-index="{{index}}" data-url="{{item.url}}">
            <image class="pic-image" src="{{item.url}}" mode="aspectFill" lazy-load="true"></image>
            <view class="pic-overlay">
                <view class="pic-title">{{item.title}}</view>
                <view class="pic-category">{{item.category}}</view>
            </view>
        </view>
    </view>

    <!-- 图片查看器 -->
    <view wx:if="{{showImageViewer}}" class="image-viewer" bindtap="closeImageViewer">
        <view class="viewer-content" catchtap="">
            <image class="viewer-image"
                   src="{{(selectedCategory === '全部' ? funPics : funPics.filter(pic => pic.category === selectedCategory))[currentImageIndex].url}}"
                   mode="aspectFit"></image>
            <view class="viewer-info">
                <view class="viewer-title">{{(selectedCategory === '全部' ? funPics : funPics.filter(pic => pic.category === selectedCategory))[currentImageIndex].title}}</view>
                <view class="viewer-desc">{{(selectedCategory === '全部' ? funPics : funPics.filter(pic => pic.category === selectedCategory))[currentImageIndex].description}}</view>
            </view>
            <view class="viewer-actions">
                <button class="save-btn" bindtap="saveImage"
                        data-url="{{(selectedCategory === '全部' ? funPics : funPics.filter(pic => pic.category === selectedCategory))[currentImageIndex].url}}">
                    保存到相册
                </button>
            </view>
        </view>
    </view>

    <view class='cu-tabbar-height'></view>
</view>
