// pages/funpic/home/<USER>
Component({
    options: {
        addGlobalClass: true,
    },
    data: {
        currentImageIndex: 0,
        showImageViewer: false,
        funPics: [
            {
                id: 1,
                title: '可爱小猫咪',
                url: 'https://cdn.pixabay.com/photo/2017/02/20/18/03/cat-2083492_1280.jpg',
                fallbackUrl: '/images/placeholder/cat.png',
                description: '超级可爱的小猫咪，萌化你的心~',
                category: '萌宠',
                height: 400
            },
            {
                id: 2,
                title: '搞笑表情包',
                url: 'https://cdn.pixabay.com/photo/2016/12/13/05/15/puppy-1903313_1280.jpg',
                fallbackUrl: '/images/placeholder/funny.png',
                description: '让你笑到停不下来的表情包',
                category: '搞笑',
                height: 350
            },
            {
                id: 3,
                title: '美丽风景',
                url: 'https://cdn.pixabay.com/photo/2015/04/23/22/00/tree-736885_1280.jpg',
                fallbackUrl: '/images/placeholder/landscape.png',
                description: '令人心旷神怡的自然风光',
                category: '风景',
                height: 500
            },
            {
                id: 4,
                title: '创意设计',
                url: 'https://cdn.pixabay.com/photo/2017/08/30/01/05/milky-way-2695569_1280.jpg',
                fallbackUrl: '/images/placeholder/creative.png',
                description: '脑洞大开的创意设计作品',
                category: '创意',
                height: 320
            },
            {
                id: 5,
                title: '治愈系插画',
                url: 'https://cdn.pixabay.com/photo/2016/05/05/02/37/sunset-1373171_1280.jpg',
                fallbackUrl: '/images/placeholder/illustration.png',
                description: '温暖治愈的手绘插画',
                category: '插画',
                height: 450
            },
            {
                id: 6,
                title: '奇趣动物',
                url: 'https://cdn.pixabay.com/photo/2017/10/20/10/58/elephant-2870777_1280.jpg',
                fallbackUrl: '/images/placeholder/animal.png',
                description: '动物世界的奇趣瞬间',
                category: '动物',
                height: 380
            },
            {
                id: 7,
                title: '梦幻星空',
                url: 'https://cdn.pixabay.com/photo/2016/10/20/18/35/earth-1756274_1280.jpg',
                fallbackUrl: '/images/placeholder/starry.png',
                description: '浩瀚星空下的无限遐想',
                category: '风景',
                height: 420
            },
            {
                id: 8,
                title: '萌萌哒小狗',
                url: 'https://cdn.pixabay.com/photo/2016/02/19/15/46/dog-1210559_1280.jpg',
                fallbackUrl: '/images/placeholder/dog.png',
                description: '忠诚可爱的小伙伴',
                category: '萌宠',
                height: 360
            },
            {
                id: 9,
                title: '抽象艺术',
                url: 'https://cdn.pixabay.com/photo/2017/08/30/01/05/milky-way-2695569_1280.jpg',
                fallbackUrl: '/images/placeholder/abstract.png',
                description: '充满想象力的抽象作品',
                category: '创意',
                height: 480
            },
            {
                id: 10,
                title: '温馨插画',
                url: 'https://cdn.pixabay.com/photo/2015/06/19/21/24/the-road-815297_1280.jpg',
                fallbackUrl: '/images/placeholder/warm.png',
                description: '温暖人心的手绘作品',
                category: '插画',
                height: 340
            }
        ],
        categories: ['全部', '萌宠', '搞笑', '风景', '创意', '插画', '动物'],
        selectedCategory: '全部',
        filteredPics: [], // 过滤后的图片列表
        leftColumnPics: [], // 左列图片
        rightColumnPics: [], // 右列图片
        leftColumnHeight: 0, // 左列高度
        rightColumnHeight: 0, // 右列高度
        imageErrorCount: {} // 图片加载错误计数
    },

    attached() {
        // 组件初始化时设置过滤后的图片
        this.updateFilteredPics();
    },
    methods: {
        // 更新过滤后的图片列表
        updateFilteredPics() {
            const filteredPics = this.getFilteredPics();
            this.setData({
                filteredPics: filteredPics
            }, () => {
                // 更新瀑布流布局
                this.updateWaterfallLayout();
            });
        },

        // 更新瀑布流布局
        updateWaterfallLayout() {
            const leftColumn = [];
            const rightColumn = [];
            let leftHeight = 0;
            let rightHeight = 0;

            this.data.filteredPics.forEach(pic => {
                // 计算图片在rpx单位下的高度（基于300rpx宽度）
                const picHeight = (pic.height * 300) / 300 + 100; // 100rpx为额外的padding和文字高度

                if (leftHeight <= rightHeight) {
                    leftColumn.push(pic);
                    leftHeight += picHeight;
                } else {
                    rightColumn.push(pic);
                    rightHeight += picHeight;
                }
            });

            this.setData({
                leftColumnPics: leftColumn,
                rightColumnPics: rightColumn,
                leftColumnHeight: leftHeight,
                rightColumnHeight: rightHeight
            });
        },

        // 查看大图
        viewImage(e) {
            const { index, column } = e.currentTarget.dataset;
            let actualIndex = 0;

            if (column === 'left') {
                const pic = this.data.leftColumnPics[index];
                actualIndex = this.data.filteredPics.findIndex(item => item.id === pic.id);
            } else {
                const pic = this.data.rightColumnPics[index];
                actualIndex = this.data.filteredPics.findIndex(item => item.id === pic.id);
            }

            this.setData({
                currentImageIndex: actualIndex,
                showImageViewer: true
            });
        },

        // 关闭图片查看器
        closeImageViewer() {
            this.setData({
                showImageViewer: false
            });
        },

        // 切换分类
        switchCategory(e) {
            const category = e.currentTarget.dataset.category;
            this.setData({
                selectedCategory: category
            }, () => {
                // 切换分类后更新过滤后的图片列表
                this.updateFilteredPics();
            });
        },

        // 获取过滤后的图片
        getFilteredPics() {
            if (this.data.selectedCategory === '全部') {
                return this.data.funPics;
            }
            return this.data.funPics.filter(pic => pic.category === this.data.selectedCategory);
        },

        // 保存图片到相册
        saveImage() {
            const currentPic = this.data.filteredPics[this.data.currentImageIndex];
            if (!currentPic) return;

            wx.showLoading({
                title: '保存中...'
            });

            wx.downloadFile({
                url: currentPic.url,
                success: (res) => {
                    wx.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath,
                        success: () => {
                            wx.hideLoading();
                            wx.showToast({
                                title: '保存成功',
                                icon: 'success'
                            });
                        },
                        fail: () => {
                            wx.hideLoading();
                            wx.showToast({
                                title: '保存失败',
                                icon: 'none'
                            });
                        }
                    });
                },
                fail: () => {
                    wx.hideLoading();
                    wx.showToast({
                        title: '下载失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 图片加载错误处理
        onImageError(e) {
            console.error('图片加载失败:', e.detail);
            const { currentTarget } = e;
            const dataset = currentTarget.dataset;

            // 尝试使用备用图片或显示占位符
            const errorCount = this.data.imageErrorCount || {};
            const imageId = dataset.id || 'unknown';

            if (!errorCount[imageId]) {
                errorCount[imageId] = 1;
                this.setData({
                    imageErrorCount: errorCount
                });

                // 可以在这里尝试重新加载或使用备用URL
                console.log('尝试重新加载图片:', imageId);
            } else {
                // 多次失败，显示提示
                wx.showToast({
                    title: '图片暂时无法显示',
                    icon: 'none',
                    duration: 1000
                });
            }
        },

        // 图片加载成功处理
        onImageLoad(e) {
            // 图片加载成功，可以在这里做一些处理
            console.log('图片加载成功');
        }
    },
})
