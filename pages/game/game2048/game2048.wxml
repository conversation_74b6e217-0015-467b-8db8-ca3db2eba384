<cu-custom bgColor="{{bgColor}}" isBack="{{true}}">
  <view slot="backText">返回</view>
  <view slot="content">{{title}}</view>
</cu-custom>

<view class="game-container">
  <!-- 游戏头部信息 -->
  <view class="game-header">
    <view class="score-container">
      <view class="score-box">
        <view class="score-label">分数</view>
        <view class="score-value">{{score}}</view>
      </view>
      <view class="score-box">
        <view class="score-label">最佳</view>
        <view class="score-value">{{bestScore}}</view>
      </view>
    </view>
    <button class="restart-btn cu-btn bg-blue" bindtap="restartGame">
      <text class="cuIcon-refresh"></text> 重新开始
    </button>
  </view>

  <!-- 游戏说明 -->
  <view class="game-instructions">
    <text>滑动合并数字，目标是达到2048！</text>
  </view>

  <!-- 游戏画布 -->
  <view class="canvas-container">
    <canvas 
      canvas-id="game-canvas" 
      id="game-canvas"
      class="game-canvas"
      bindtouchstart="onTouchStart"
      bindtouchend="onTouchEnd"
      disable-scroll="true">
    </canvas>
  </view>

  <!-- 游戏控制说明 -->
  <view class="control-instructions">
    <view class="instruction-item">
      <text class="cuIcon-up text-blue"></text>
      <text class="cuIcon-down text-blue"></text>
      <text class="cuIcon-left text-blue"></text>
      <text class="cuIcon-right text-blue"></text>
    </view>
    <text class="instruction-text">滑动屏幕来移动数字方块</text>
  </view>
</view>
