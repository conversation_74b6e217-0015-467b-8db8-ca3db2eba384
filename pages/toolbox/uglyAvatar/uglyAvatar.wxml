<cu-custom bgColor="{{bgColor}}" isBack="{{true}}">
    <view slot="backText">返回</view>
    <view slot="content">{{title}}生成器</view>
</cu-custom>
<view class="avatar-container">
    <!-- Canvas区域 -->
    <view class="canvas-wrapper">
        <canvas type="2d" id="faceCanvas" class="face-canvas"></canvas>
        <view class="canvas-overlay" wx:if="{{loading}}">
            <view class="loading-spinner"></view>
            <text class="loading-text">生成中...</text>
        </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons">
        <view class="button-wrapper">
            <button class="action-btn generate-btn" bindtap="generateFace">
                <view class="btn-icon">🎲</view>
                <view class="btn-text">随机生成</view>
            </button>
        </view>
        <view class="button-wrapper">
            <button class="action-btn download-btn" bindtap="downloadCanvasAsImage">
                <view class="btn-icon">💾</view>
                <view class="btn-text">保存头像</view>
            </button>
        </view>
    </view>

    <!-- 提示信息 -->
    <view class="tip-section">
        <view class="tip-text">💡 点击"随机生成"创建独特的丑丑头像</view>
    </view>
</view>
