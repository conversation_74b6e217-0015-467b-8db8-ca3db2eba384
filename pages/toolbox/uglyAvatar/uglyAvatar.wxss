/* 丑丑头像生成器样式 */
.avatar-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
}

/* Canvas区域 */
.canvas-wrapper {
  position: relative;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.face-canvas {
  width: 100%;
  height: 600rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255,255,255,0.9);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #ff6b9d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.button-wrapper {
  flex: 1;
}

.action-btn {
  width: 100%;
  background: rgba(255,255,255,0.9);
  border: none;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 6rpx 24rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

.generate-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
}

.download-btn {
  background: linear-gradient(45deg, #ff6b9d, #c44569);
  color: #fff;
}

.btn-icon {
  font-size: 48rpx;
  line-height: 1;
}

.btn-text {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1;
}

/* 提示信息 */
.tip-section {
  background: rgba(255,255,255,0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
}

.tip-text {
  font-size: 26rpx;
  color: rgba(255,255,255,0.9);
  text-align: center;
  line-height: 1.4;
}
