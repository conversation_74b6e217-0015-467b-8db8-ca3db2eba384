<view class="toolbox-container">
    <!-- 头部区域 -->
    <view class="toolbox-header">
        <view class="header-content">
            <view class="header-title">🛠️ 工具箱</view>
            <view class="header-subtitle">实用工具集合，让生活更便捷</view>
        </view>
        <view class="header-stats">
            <view class="stats-item">
                <view class="stats-number">{{availableToolsCount}}</view>
                <view class="stats-label">可用工具</view>
            </view>
        </view>
    </view>

    <!-- 工具网格区域 -->
    <scroll-view scroll-y class="tools-scroll" enhanced="true" show-scrollbar="false">
        <view class="tools-grid">
            <navigator wx:for="{{elements}}" wx:key="name" wx:if="{{item.show}}"
                      hover-class="tool-card-hover"
                      url="/pages/{{item.model}}/{{item.name}}/{{item.name}}?title={{item.title}}&color={{item.color}}"
                      class="tool-card">
                <view class="tool-icon-wrapper bg-{{item.color}}">
                    <text class="tool-icon cuIcon-{{item.icon}}"></text>
                </view>
                <view class="tool-info">
                    <view class="tool-title">{{item.title}}</view>
                    <view class="tool-desc">{{item.description || '实用工具'}}</view>
                </view>
                <view class="tool-arrow">
                    <text class="cuIcon-right"></text>
                </view>
            </navigator>
        </view>

        <!-- 即将上线提示 -->
        <view class="coming-soon-section">
            <view class="coming-soon-content">
                <view class="coming-soon-icon">🚀</view>
                <view class="coming-soon-title">更多工具</view>
                <view class="coming-soon-text">正在开发中，敬请期待...</view>
            </view>
        </view>

        <view class='cu-tabbar-height'></view>
    </scroll-view>
</view>
