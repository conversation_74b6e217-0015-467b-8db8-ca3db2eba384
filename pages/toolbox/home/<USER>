// pages/toolbox/home/<USER>
Component({
    options: {
        addGlobalClass: true,
    },
    data: {
        elements: [
            {
                title: '文件预览',
                name: 'filePreview',
                color: 'purple',
                icon: 'read',
                model: 'basics',
                description: '支持多种文件格式在线预览',
                show: true,
            },
            {
                title: '练单词',
                name: 'wordGuessing',
                color: 'mauve',
                icon: 'formfill',
                model: 'toolbox',
                description: '趣味单词猜测游戏',
                show: false,
            },
            {
                title: '一键抠图',
                name: 'cutout',
                color: 'cyan',
                icon: 'picfill',
                model: 'toolbox',
                description: 'AI智能抠图，一键去背景',
                show: false,
            },
            {
                title: '丑丑头像',
                name: 'uglyAvatar',
                color: 'pink',
                icon: 'album',
                model: 'toolbox',
                description: '生成个性化卡通头像',
                show: true,
            },
            {
                title: '证件照',
                name: 'idPhoto',
                color: 'orange',
                icon: 'camerafill',
                model: 'toolbox',
                description: '制作标准证件照片',
                show: true,
            },
            {
                title: '缩写联想',
                name: 'acronymTranslator',
                color: 'blue',
                icon: 'text',
                model: 'toolbox',
                description: '英文缩写智能翻译',
                show: true,
            },
        ],
        availableToolsCount: 0
    },

    attached() {
        // 计算可用工具数量
        const availableCount = this.data.elements.filter(item => item.show).length;
        this.setData({
            availableToolsCount: availableCount
        });
    },
})
