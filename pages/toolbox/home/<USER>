/* pages/toolbox/home/<USER>/
.toolbox-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
}

/* 头部区域 */
.toolbox-header {
    padding: 60rpx 40rpx 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.header-content {
    flex: 1;
}

.header-title {
    font-size: 52rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 16rpx;
    text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.header-subtitle {
    font-size: 28rpx;
    color: rgba(255,255,255,0.8);
    line-height: 1.4;
}

.header-stats {
    display: flex;
    align-items: center;
}

.stats-item {
    text-align: center;
    background: rgba(255,255,255,0.15);
    padding: 20rpx 30rpx;
    border-radius: 20rpx;
    backdrop-filter: blur(10rpx);
}

.stats-number {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    line-height: 1;
}

.stats-label {
    font-size: 22rpx;
    color: rgba(255,255,255,0.8);
    margin-top: 8rpx;
}

/* 工具滚动区域 */
.tools-scroll {
    flex: 1;
    padding: 0 30rpx;
}

/* 工具网格 */
.tools-grid {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-bottom: 40rpx;
}

/* 工具卡片 */
.tool-card {
    background: rgba(255,255,255,0.95);
    border-radius: 24rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10rpx);
}

.tool-card-hover {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

/* 工具图标区域 */
.tool-icon-wrapper {
    width: 80rpx;
    height: 80rpx;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.tool-icon {
    font-size: 36rpx;
    color: #fff;
}

/* 工具信息区域 */
.tool-info {
    flex: 1;
}

.tool-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
    line-height: 1.2;
}

.tool-desc {
    font-size: 24rpx;
    color: #666;
    line-height: 1.3;
}

/* 箭头 */
.tool-arrow {
    color: #ccc;
    font-size: 24rpx;
    margin-left: 16rpx;
}

/* 即将上线区域 */
.coming-soon-section {
    margin: 40rpx 0;
    padding: 0 20rpx;
}

.coming-soon-content {
    background: rgba(255,255,255,0.1);
    border-radius: 24rpx;
    padding: 60rpx 40rpx;
    text-align: center;
    border: 2rpx dashed rgba(255,255,255,0.3);
    backdrop-filter: blur(10rpx);
}

.coming-soon-icon {
    font-size: 64rpx;
    margin-bottom: 20rpx;
}

.coming-soon-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 16rpx;
}

.coming-soon-text {
    font-size: 26rpx;
    color: rgba(255,255,255,0.8);
    line-height: 1.4;
}