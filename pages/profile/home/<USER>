@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.UCenter-bg {
  background: linear-gradient(270deg, #C7E1F0, #96C4E6, #194D5B, #E560CD, #FAAE4E);
  background-size: 1000% 1000%; /* 让背景图的尺寸比容器大 */
  animation: gradientAnimation 15s ease infinite; /* 动画持续时间和效果 */
  height: 550rpx;
  display: flex;
  justify-content: center;
  padding-top: 40rpx;
  overflow: hidden;
  position: relative;
  flex-direction: column;
  align-items: center;
  color: #fff;
  font-weight: 300;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.UCenter-bg text {
  opacity: 0.8;
}

.UCenter-bg image {
  width: 200rpx;
  height: 200rpx;
}

.UCenter-bg .gif-wave {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 99;
  mix-blend-mode: screen;
  height: 100rpx;
}

map, .mapBox {
  left: 0;
  z-index: 99;
  mix-blend-mode: screen;
  height: 100rpx;
}

map, .mapBox {
  width: 750rpx;
  height: 300rpx;
}

/* 调试切换按钮 */
.debug-switch {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.switch-tip {
  font-size: 20rpx;
  opacity: 0.8;
  margin-top: 5rpx;
}

/* 未登录用户样式 */
.guest-bg {
  background: linear-gradient(270deg, #667eea, #764ba2) !important;
}

.guest-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  color: rgba(255,255,255,0.8);
  border: 3rpx solid rgba(255,255,255,0.3);
}

.login-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.5);
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  margin-top: 40rpx;
  font-size: 28rpx;
}

.login-btn:active {
  background: rgba(255,255,255,0.3);
}

/* 调试切换按钮 */
.debug-switch {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.switch-tip {
  font-size: 20rpx;
  opacity: 0.8;
  margin-top: 5rpx;
}

/* 未登录用户样式 */
.guest-bg {
  background: linear-gradient(270deg, #667eea, #764ba2) !important;
}

.guest-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  color: rgba(255,255,255,0.8);
  border: 3rpx solid rgba(255,255,255,0.3);
}

.login-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 2rpx solid rgba(255,255,255,0.5);
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  margin-top: 40rpx;
  font-size: 28rpx;
}

.login-btn:active {
  background: rgba(255,255,255,0.3);
}

/* 普通用户样式 */
.user-bg {
  background: linear-gradient(270deg, #4facfe, #00f2fe) !important;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255,255,255,0.3);
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-level {
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 20rpx;
}

.user-join {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 超级管理员样式 */
.admin-bg {
  background: linear-gradient(270deg, #ff9a9e, #fecfef, #fecfef) !important;
}

.admin-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: rgba(255,215,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  color: #ffd700;
  border: 3rpx solid rgba(255,215,0,0.5);
  box-shadow: 0 0 20rpx rgba(255,215,0,0.3);
}

.cu-avatar {
  font-variant: small-caps;
  margin: 0;
  padding: 0;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  background-color: #ccc;
  color: var(--white);
  white-space: nowrap;
  position: relative;
  width: 160rpx;
  height: 160rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
  font-size: 1.5em;
  background-image: url("https://img.tuytuy.com/2024/07/88af384e698c5b47819c8be2bb3f33b8.png");
}
