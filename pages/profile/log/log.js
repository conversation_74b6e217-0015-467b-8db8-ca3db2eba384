const app = getApp();
// pages/profile/log/log.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    StatusBar: app.globalData.StatusBar,
    CustomBar: app.globalData.CustomBar,
    ColorList: app.globalData.ColorList,
    versionLogs: [
      {
        version: 'v2.1.0',
        date: '2025-06-03',
        type: 'major',
        changes: [
          '1.交换热点讯息和工具箱位置，优化用户体验',
          '2.将"关于"页面改为"我的"页面，简化用户界面',
          '3.移除模拟切换逻辑，默认显示登录页面',
          '4.新增2048经典数字游戏，提供更好的娱乐体验',
          '5.移除撕裂模拟相关代码，提升代码质量',
          '6.修复banner图片数据过滤问题，确保图片正常显示',
          '7.完善更新日志页面，基于git提交记录整理版本信息'
        ]
      },
      {
        version: 'v2.0.5',
        date: '2025-04-11',
        type: 'minor',
        changes: [
          '1.更新README，增加小程序体验部分及预览图片链接',
          '2.更新图片资源链接，修正图片路径',
          '3.更新项目介绍，增加预览图片'
        ]
      },
      {
        version: 'v2.0.0',
        date: '2024-09-02',
        type: 'major',
        changes: [
          '1.调整新闻展示方式，优化阅读体验',
          '2.增加富文本组件处理功能',
          '3.新增弹窗显示详情功能',
          '4.修改应用名称，提升品牌识别度'
        ]
      },
      {
        version: 'v1.3.0',
        date: '2024-08-30',
        type: 'minor',
        changes: [
          '1.替换对话的头像框，提升视觉效果',
          '2.恢复后台接口的访问功能',
          '3.修复丑丑头像底部空白问题',
          '4.新增短语词联想功能，提升用户体验'
        ]
      },
      {
        version: 'v1.2.0',
        date: '2024-08-15',
        type: 'minor',
        changes: [
          '1.优化一键抠图选择图片的弹框',
          '2.删除过大的gif文件，提升加载速度',
          '3.新增相框功能',
          '4.优化绘制逻辑，优先绘制背景色',
          '5.新增下载按钮，支持更改照片背景色',
          '6.完成抠图结合功能'
        ]
      },
      {
        version: 'v1.1.0',
        date: '2024-08-04',
        type: 'minor',
        changes: [
          '1.添加一键抠图功能',
          '2.优化图片处理算法'
        ]
      },
      {
        version: 'v1.0.1',
        date: '2024-08-02',
        type: 'patch',
        changes: [
          '1.添加丑丑头像生成器',
          '2.修复已知问题'
        ]
      },
      {
        version: 'v1.0.0',
        date: '2024-07-25',
        type: 'major',
        changes: [
          '1.添加文件预览功能',
          '2.项目初始版本发布'
        ]
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  pageBack() {
    wx.navigateBack({
      delta: 1
    });
  }
})
