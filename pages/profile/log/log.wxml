<cu-custom bgColor="bg-gradual-green" isBack="{{true}}">
  <view slot="backText">返回</view>
  <view slot="content">更新日志</view>
</cu-custom>

<view class="log-container">
  <!-- 版本统计信息 -->
  <view class="version-stats">
    <view class="stats-card">
      <view class="stats-number">{{versionLogs.length}}</view>
      <view class="stats-label">版本总数</view>
    </view>
    <view class="stats-card">
      <view class="stats-number">{{versionLogs[0].version}}</view>
      <view class="stats-label">当前版本</view>
    </view>
    <view class="stats-card">
      <view class="stats-number">{{versionLogs[0].date}}</view>
      <view class="stats-label">最新更新</view>
    </view>
  </view>

  <!-- 版本日志时间线 -->
  <view class="cu-timeline">
    <!-- 动态版本日志列表 -->
    <view wx:for="{{versionLogs}}" wx:key="version" class="cu-item text-green">
      <view class="bg-{{item.type === 'major' ? 'gradual-green' : item.type === 'minor' ? 'gradual-blue' : 'gradual-orange'}} content shadow">
        <view class="cu-capsule radius">
          <view class="cu-tag bg-white text-{{item.type === 'major' ? 'green' : item.type === 'minor' ? 'blue' : 'orange'}}">
            {{item.version}}
            <text class="version-type">{{item.type === 'major' ? '重大更新' : item.type === 'minor' ? '功能更新' : '修复更新'}}</text>
          </view>
          <view class="cu-tag line-white">{{item.date}}</view>
        </view>
        <view class="margin-top-sm text-content">
          <view wx:for="{{item.changes}}" wx:key="*this" wx:for-item="change" class="change-item">
            {{change}}
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
