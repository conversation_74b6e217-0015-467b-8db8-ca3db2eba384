page {
  background: #f8f9fa;
}

.log-container {
  padding: 20rpx;
}

/* 版本统计信息样式 */
.version-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.stats-card {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
}

.stats-card:not(:last-child) {
  border-right: 1rpx solid #eee;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #39b54a;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 版本类型标签样式 */
.version-type {
  font-size: 20rpx;
  margin-left: 8rpx;
  opacity: 0.8;
}

/* 更新项样式 */
.change-item {
  margin-bottom: 8rpx;
  padding-left: 20rpx;
  position: relative;
  line-height: 1.6;
}

.change-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #39b54a;
  font-weight: bold;
}

/* 时间线优化 */
.cu-timeline .cu-item .content {
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.cu-timeline .cu-item .cu-capsule {
  margin-bottom: 20rpx;
}

.cu-timeline .cu-item .cu-capsule .cu-tag {
  font-size: 26rpx;
  padding: 8rpx 16rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .version-stats {
    flex-direction: column;
  }

  .stats-card:not(:last-child) {
    border-right: none;
    border-bottom: 1rpx solid #eee;
  }

  .stats-card {
    padding: 15rpx;
  }
}

/* 动画效果 */
.cu-timeline .cu-item .content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
