const app = getApp();
const API = app.globalData.API_ENDPOINTS.HOT;

Component({
    options: {
        addGlobalClass: true,
    },
    data: {
        CustomBar: app.globalData.CustomBar,
        selectedPlatform: 0, // 当前选中的平台索引
        platformList: [], // 平台列表
        hotNewsList: [], // 热点新闻列表
        loading: false, // 加载状态
        refreshing: false, // 刷新状态
        showNewsDetail: false, // 显示新闻详情
        currentNews: null, // 当前查看的新闻
        searchKeyword: '', // 搜索关键词
        showSearch: false, // 显示搜索框
        lastUpdateTime: '', // 最后更新时间
        showPlatformModal: false, // 显示平台选择弹窗
    },
    attached() {
        this.initPage();
    },

    ready() {
        // 页面准备完成
    },
    methods: {
        // 初始化页面
        async initPage() {
            this.setData({ loading: true });
            try {
                await this.loadPlatformList();
                if (this.data.platformList.length > 0) {
                    await this.loadHotNews(0);
                }
            } catch (error) {
                console.error('初始化失败:', error);
                wx.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
            } finally {
                this.setData({ loading: false });
            }
        },

        // 加载平台列表
        async loadPlatformList() {
            try {
                const res = await app.$http.get(API.SIDE_BAR_LIST);
                if (res.ok && res.data) {
                    // 按sort字段排序并添加图标
                    const iconMap = {
                        'weibo': '🔥',
                        'zhihu': '🧠',
                        'douyin': '🎵',
                        'baidu': '🔍',
                        'toutiao': '📰',
                        'bilibili': '📺',
                        'kuaishou': '⚡',
                        'xiaohongshu': '📖'
                    };

                    const sortedList = res.data.sort((a, b) => a.sort - b.sort).map(item => ({
                        ...item,
                        icon: iconMap[item.name] || '📱'
                    }));

                    this.setData({
                        platformList: sortedList,
                        selectedPlatform: 0
                    });
                }
            } catch (error) {
                console.error('加载平台列表失败:', error);
                throw error;
            }
        },

        // 显示平台选择弹窗
        showPlatformPicker() {
            this.setData({ showPlatformModal: true });
        },

        // 隐藏平台选择弹窗
        hidePlatformPicker() {
            this.setData({ showPlatformModal: false });
        },

        // 选择平台
        async selectPlatform(e) {
            const index = e.currentTarget.dataset.index;
            if (index === this.data.selectedPlatform) {
                this.hidePlatformPicker();
                return;
            }

            this.setData({
                selectedPlatform: index,
                showPlatformModal: false
            });
            await this.loadHotNews(index);
        },
        // 加载热点新闻
        async loadHotNews(platformIndex) {
            if (platformIndex >= this.data.platformList.length) return;

            this.setData({ loading: true });
            try {
                const platform = this.data.platformList[platformIndex];
                const res = await app.$http.get(`${API.GET_HOT_NEWS}/${platform.name}`);

                if (res.ok && res.data) {
                    // 为每条新闻添加索引和格式化热度
                    const newsList = res.data.map((item, index) => ({
                        ...item,
                        index: index + 1,
                        formattedHot: this.formatHotNumber(item.hot),
                        platform: platform.chineseName
                    }));

                    this.setData({
                        hotNewsList: newsList,
                        lastUpdateTime: this.getCurrentTime()
                    });
                }
            } catch (error) {
                console.error('加载新闻失败:', error);
                wx.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
            } finally {
                this.setData({ loading: false });
            }
        },
        // 刷新当前平台新闻
        async refreshNews() {
            this.setData({ refreshing: true });
            try {
                await this.loadHotNews(this.data.selectedPlatform);
                wx.showToast({
                    title: '刷新成功',
                    icon: 'success',
                    duration: 1000
                });
            } catch (error) {
                wx.showToast({
                    title: '刷新失败',
                    icon: 'none'
                });
            } finally {
                this.setData({ refreshing: false });
            }
        },
        // 查看新闻详情
        viewNewsDetail(e) {
            const index = e.currentTarget.dataset.index;
            const news = this.data.hotNewsList[index];

            if (!news) return;

            this.setData({
                currentNews: news,
                showNewsDetail: true
            });
        },
        // 关闭新闻详情
        closeNewsDetail() {
            this.setData({
                showNewsDetail: false,
                currentNews: null
            });
        },
        // 跳转到原文链接
        openOriginalLink() {
            const news = this.data.currentNews;
            if (!news || !news.url) return;

            // 复制链接到剪贴板
            wx.setClipboardData({
                data: news.url,
                success: () => {
                    wx.showToast({
                        title: '链接已复制',
                        icon: 'success'
                    });
                }
            });
        },
        // 格式化热度数字
        formatHotNumber(hot) {
            if (!hot) return '0';
            if (hot >= 10000) {
                return (hot / 10000).toFixed(1) + 'w';
            }
            return hot.toString();
        },

        // 获取当前时间
        getCurrentTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        },

        // 搜索功能
        toggleSearch() {
            this.setData({
                showSearch: !this.data.showSearch,
                searchKeyword: ''
            });
        },

        // 搜索输入
        onSearchInput(e) {
            this.setData({
                searchKeyword: e.detail.value
            });
        },

        // 执行搜索
        performSearch() {
            const keyword = this.data.searchKeyword.trim();
            if (!keyword) {
                wx.showToast({
                    title: '请输入搜索关键词',
                    icon: 'none'
                });
                return;
            }

            // 在当前新闻列表中搜索
            const filteredNews = this.data.hotNewsList.filter(news =>
                news.title.toLowerCase().includes(keyword.toLowerCase()) ||
                (news.desc && news.desc.toLowerCase().includes(keyword.toLowerCase()))
            );

            if (filteredNews.length === 0) {
                wx.showToast({
                    title: '未找到相关新闻',
                    icon: 'none'
                });
            } else {
                this.setData({
                    hotNewsList: filteredNews
                });
                wx.showToast({
                    title: `找到${filteredNews.length}条相关新闻`,
                    icon: 'success'
                });
            }
        },

        // 清除搜索
        clearSearch() {
            this.setData({
                searchKeyword: '',
                showSearch: false
            });
            // 重新加载当前平台新闻
            this.loadHotNews(this.data.selectedPlatform);
        }
    }
});
