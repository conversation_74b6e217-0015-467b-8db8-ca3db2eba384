/* 热点讯息页面样式 */
.hot-news-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

/* 头部区域 */
.header-section {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10rpx);
  padding: 40rpx 30rpx 20rpx;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 44rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(255,255,255,0.3);
}

/* 搜索区域 */
.search-section {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.search-section.show {
  max-height: 120rpx;
  margin-bottom: 20rpx;
}

.search-box {
  background: rgba(255,255,255,0.9);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-actions {
  display: flex;
  gap: 20rpx;
  margin-left: 20rpx;
}

.search-btn, .clear-btn {
  font-size: 32rpx;
  color: #666;
  padding: 10rpx;
}

.update-time {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  text-align: center;
}

/* 旋转动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 主体内容区域 */
.main-content {
  display: flex;
  flex: 1;
  background: #f8f9fa;
  height: calc(100vh - 280rpx); /* 减去头部高度 */
}

/* 左侧平台导航 */
.platform-nav {
  width: 140rpx; /* 调整为2个汉字的宽度 */
  background: rgba(255,255,255,0.95);
  box-shadow: 4rpx 0 20rpx rgba(0,0,0,0.1);
  flex-shrink: 0; /* 防止被压缩 */
}

.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  position: relative;
  transition: all 0.3s ease;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 120rpx; /* 确保最小高度 */
}

.platform-item:active {
  background: #f0f0f0;
}

.platform-item.active {
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
}

.platform-icon {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  flex-shrink: 0;
}

.platform-name {
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  line-height: 1.3;
  color: #333;
  width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  overflow: hidden;
}

.platform-item.active .platform-name {
  color: #667eea;
  font-weight: bold;
}

.platform-indicator {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: #667eea;
  border-radius: 6rpx 0 0 6rpx;
}

/* 新闻列表区域 */
.news-list {
  flex: 1;
  padding: 30rpx;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 新闻卡片 */
.news-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.news-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
}

.news-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.12);
}

.rank-badge {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e0e0e0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.rank-badge.top-rank {
  background: linear-gradient(45deg, #ff6b9d, #c44569);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}

/* 新闻图片 */
.news-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-content.with-image {
  margin-right: 20rpx;
}

.news-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.news-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.news-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hot-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.hot-icon {
  font-size: 24rpx;
}

.hot-number {
  font-size: 24rpx;
  color: #ff6b9d;
  font-weight: bold;
}

.platform-tag {
  background: #f0f0f0;
  color: #666;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.arrow-indicator {
  color: #ccc;
  font-size: 24rpx;
  flex-shrink: 0;
  align-self: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 30rpx;
}

.empty-action {
  background: #667eea;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
}

/* 新闻详情弹窗 */
.news-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.news-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.detail-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.news-detail-modal.show .detail-content {
  transform: scale(1);
}

.detail-header {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
  padding: 40rpx 30rpx;
  position: relative;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1.4;
  padding-right: 80rpx;
}

.close-btn {
  position: absolute;
  top: 40rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.detail-meta {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 16rpx;
}

.meta-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.meta-value.hot {
  color: #ff6b9d;
}

.detail-description {
  padding: 30rpx;
  max-height: 400rpx;
  overflow-y: auto;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.detail-actions {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-button {
  width: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.action-button:active {
  transform: scale(0.98);
}
