<view class="hot-news-container">
    <!-- 头部区域 -->
    <view class="header-section">
        <view class="header-content">
            <view class="header-title">
                <text class="title-icon">🔥</text>
                <text class="title-text">热点讯息</text>
            </view>
            <view class="header-actions">
                <view class="action-btn" bindtap="toggleSearch">
                    <text class="cuIcon-search"></text>
                </view>
                <view class="action-btn" bindtap="refreshNews">
                    <text class="cuIcon-refresh {{refreshing ? 'rotating' : ''}}"></text>
                </view>
            </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-section {{showSearch ? 'show' : ''}}">
            <view class="search-box">
                <input class="search-input"
                       placeholder="搜索新闻标题..."
                       value="{{searchKeyword}}"
                       bindinput="onSearchInput"
                       bindconfirm="performSearch" />
                <view class="search-actions">
                    <text class="cuIcon-search search-btn" bindtap="performSearch"></text>
                    <text class="cuIcon-close clear-btn" bindtap="clearSearch"></text>
                </view>
            </view>
        </view>

        <!-- 更新时间 -->
        <view class="update-time" wx:if="{{lastUpdateTime}}">
            最后更新：{{lastUpdateTime}}
        </view>
    </view>


    <!-- 主体内容区域 -->
    <view class="main-content">
        <!-- 左侧平台导航 -->
        <scroll-view class="platform-nav" scroll-y="true" enhanced="true" show-scrollbar="false">
            <view wx:for="{{platformList}}" wx:key="name"
                  class="platform-item {{selectedPlatform === index ? 'active' : ''}}"
                  bindtap="switchPlatform" data-index="{{index}}">
                <view class="platform-icon">{{item.icon}}</view>
                <view class="platform-name">{{item.chineseName}}</view>
                <view class="platform-indicator" wx:if="{{selectedPlatform === index}}"></view>
            </view>
        </scroll-view>

        <!-- 右侧新闻列表 -->
        <scroll-view class="news-list" scroll-y="true" enhanced="true" show-scrollbar="false">
            <view class="loading-indicator" wx:if="{{loading}}">
                <view class="loading-spinner"></view>
                <text>加载中...</text>
            </view>

            <view class="news-grid" wx:if="{{!loading}}">
                <view wx:for="{{hotNewsList}}" wx:key="index"
                      class="news-card" bindtap="viewNewsDetail" data-index="{{index}}">
                    <!-- 排名标识 -->
                    <view class="rank-badge {{index < 3 ? 'top-rank' : ''}}">
                        {{item.index}}
                    </view>

                    <!-- 新闻图片 -->
                    <view class="news-image" wx:if="{{item.cover}}">
                        <image src="{{item.cover}}" mode="aspectFill" class="cover-image" lazy-load="true"></image>
                    </view>

                    <!-- 新闻内容 -->
                    <view class="news-content {{item.cover ? 'with-image' : ''}}">
                        <view class="news-title">{{item.title}}</view>
                        <view class="news-desc" wx:if="{{item.desc}}">{{item.desc}}</view>
                        <view class="news-meta">
                            <view class="hot-indicator">
                                <text class="hot-icon">🔥</text>
                                <text class="hot-number">{{item.formattedHot}}</text>
                            </view>
                            <view class="platform-tag">{{item.platform}}</view>
                        </view>
                    </view>

                    <!-- 箭头指示器 -->
                    <view class="arrow-indicator">
                        <text class="cuIcon-right"></text>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <view class="empty-state" wx:if="{{!loading && hotNewsList.length === 0}}">
                <view class="empty-icon">📰</view>
                <view class="empty-text">暂无新闻数据</view>
                <view class="empty-action" bindtap="refreshNews">点击刷新</view>
            </view>

            <view class='cu-tabbar-height'></view>
        </scroll-view>
    </view>
</view>

<!-- 新闻详情弹窗 -->
<view class="news-detail-modal {{showNewsDetail ? 'show' : ''}}" bindtap="closeNewsDetail">
    <view class="detail-content" catchtap="">
        <view class="detail-header">
            <view class="detail-title">{{currentNews.title}}</view>
            <view class="close-btn" bindtap="closeNewsDetail">
                <text class="cuIcon-close"></text>
            </view>
        </view>

        <view class="detail-meta">
            <view class="meta-item">
                <text class="meta-label">平台：</text>
                <text class="meta-value">{{currentNews.platform}}</text>
            </view>
            <view class="meta-item">
                <text class="meta-label">热度：</text>
                <text class="meta-value hot">🔥 {{currentNews.formattedHot}}</text>
            </view>
        </view>

        <view class="detail-description" wx:if="{{currentNews.formattedDesc}}">
            <rich-text nodes="{{currentNews.formattedDesc}}"></rich-text>
        </view>

        <view class="detail-actions">
            <button class="action-button primary" bindtap="openOriginalLink">
                <text class="cuIcon-link"></text>
                <text>复制原文链接</text>
            </button>
        </view>
    </view>
</view>
